import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { FormField, Page } from "@/data/templates";
import {
  GripVertical,
  Type,
  Calendar,
  Hash,
  Mail,
  MessageSquare,
  CheckSquare,
  MousePointerClick,
  Table,
  Image,
  ListChecks,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Copy,
  CopyPlus,
  Trash2,
  ChevronsUp,
  ChevronsDown,
  Group,
  Ungroup,
  Move,
  Square,
  Minus,
  ArrowRight,
  QrCode,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "./ui/button";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { useEditorUIContext } from "@/context/EditorUIContext";

interface SortableFieldItemProps {
  field: FormField;
  isSelected: boolean;
  isGroupSelection: boolean;
  activeFieldIds: string[];
  onSelect: (id: string, isMulti: boolean) => void;
  onContextMenuSelect: (id: string) => void;
  onToggleVisibility: (id: string) => void;
  onToggleLock: (id: string) => void;
  onCopy: (ids: string[]) => void;
  onDuplicate: (ids: string[]) => void;
  onDelete: (ids: string[]) => void;
  onBringToFront: (id: string) => void;
  onSendToBack: (id: string) => void;
  onGroup: (ids: string[]) => void;
  onUngroup: (ids: string[]) => void;
  onMoveToPage: (fieldIds: string[], targetPageId: string) => void;
  pages: Page[];
  currentPageId: string;
  isFirstInGroup?: boolean;
  isLastInGroup?: boolean;
}

const fieldIcons: Record<FormField["type"], React.ElementType> = {
  text: Type,
  textarea: MessageSquare,
  select: CheckSquare,
  radio: ListChecks,
  checkbox: CheckSquare,
  signature: MousePointerClick,
  "static-text": Type,
  table: Table,
  date: Calendar,
  number: Hash,
  email: Mail,
  image: Image,
  box: Square,
  line: Minus,
  arrow: ArrowRight,
  qrcode: QrCode,
};

export const SortableFieldItem = React.forwardRef<HTMLDivElement, SortableFieldItemProps>((props, ref) => {
  const {
    field,
    isSelected,
    isGroupSelection,
    activeFieldIds,
    onSelect,
    onContextMenuSelect,
    onToggleVisibility,
    onToggleLock,
    onCopy,
    onDuplicate,
    onDelete,
    onBringToFront,
    onSendToBack,
    onGroup,
    onUngroup,
    onMoveToPage,
    pages,
    currentPageId,
    isFirstInGroup,
    isLastInGroup,
  } = props;

  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: field.id });
  const { setHoveredFieldId } = useEditorUIContext();

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const combinedRef = (node: HTMLDivElement | null) => {
    setNodeRef(node);
    if (typeof ref === 'function') {
      ref(node);
    } else if (ref) {
      ref.current = node;
    }
  };

  const Icon = fieldIcons[field.type] || Type;

  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <div
          ref={combinedRef}
          style={style}
          className={cn(
            "flex items-center gap-2 p-2 rounded-md border border-transparent transition-colors relative",
            isSelected
              ? "bg-blue-100 dark:bg-blue-900/30 border-blue-500"
              : "hover:bg-accent",
            field.groupId && "pl-6"
          )}
          onClick={(e) => onSelect(field.id, e.shiftKey)}
          onContextMenu={() => onContextMenuSelect(field.id)}
          onMouseEnter={() => setHoveredFieldId(field.id)}
          onMouseLeave={() => setHoveredFieldId(null)}
        >
          {field.groupId && (
            <div className={cn(
              "absolute left-2 top-0 w-[1.5px] bg-gray-400 dark:bg-gray-500",
              isFirstInGroup ? "h-1/2 top-1/2" : "h-full",
              isLastInGroup && "h-1/2"
            )} />
          )}
          {field.groupId && (
            <div className="absolute left-2 top-1/2 h-[1.5px] w-2 bg-gray-400 dark:bg-gray-500" />
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 cursor-grab"
            {...attributes}
            {...listeners}
          >
            <GripVertical className="h-4 w-4 text-muted-foreground" />
          </Button>
          <Icon className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm flex-grow truncate">{field.label}</span>
          <div className="ml-auto flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                onToggleVisibility(field.id);
              }}
            >
              {field.isVisible ?? true ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                onToggleLock(field.id);
              }}
            >
              {field.isLocked ? (
                <Lock className="h-4 w-4" />
              ) : (
                <Unlock className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent onClick={(e) => e.stopPropagation()}>
        {activeFieldIds.length > 0 && (
          <>
            <ContextMenuItem onClick={() => onCopy(activeFieldIds)}>
              <Copy className="mr-2 h-4 w-4" /> Copy
            </ContextMenuItem>
            <ContextMenuItem onClick={() => onDuplicate(activeFieldIds)}>
              <CopyPlus className="mr-2 h-4 w-4" /> Duplicate
            </ContextMenuItem>
            {activeFieldIds.length > 1 ? (
              isGroupSelection ? (
                <ContextMenuItem onClick={() => onUngroup(activeFieldIds)}>
                  <Ungroup className="mr-2 h-4 w-4" /> Ungroup
                </ContextMenuItem>
              ) : (
                <ContextMenuItem onClick={() => onGroup(activeFieldIds)}>
                  <Group className="mr-2 h-4 w-4" /> Group
                </ContextMenuItem>
              )
            ) : null}
            <ContextMenuSeparator />
            <ContextMenuSub>
              <ContextMenuSubTrigger disabled={pages.length <= 1}>
                <Move className="mr-2 h-4 w-4" /> Move to Page
              </ContextMenuSubTrigger>
              <ContextMenuSubContent>
                {pages.map((page, index) => (
                  page.id !== currentPageId && (
                    <ContextMenuItem key={page.id} onClick={() => onMoveToPage(activeFieldIds, page.id)}>
                      Page {index + 1}
                    </ContextMenuItem>
                  )
                ))}
              </ContextMenuSubContent>
            </ContextMenuSub>
            <ContextMenuItem onClick={() => onBringToFront(field.id)}>
              <ChevronsUp className="mr-2 h-4 w-4" /> Bring to Front
            </ContextMenuItem>
            <ContextMenuItem onClick={() => onSendToBack(field.id)}>
              <ChevronsDown className="mr-2 h-4 w-4" /> Send to Back
            </ContextMenuItem>
            <ContextMenuSeparator />
            <ContextMenuItem
              className="text-red-600"
              onClick={() => onDelete(activeFieldIds)}
            >
              <Trash2 className="mr-2 h-4 w-4" /> Delete
            </ContextMenuItem>
          </>
        )}
      </ContextMenuContent>
    </ContextMenu>
  );
});

SortableFieldItem.displayName = 'SortableFieldItem';