import { FormField } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  ArrowUp,
  ArrowDown,
  ChevronsUp,
  ChevronsDown,
} from "lucide-react";

interface LayoutPanelProps {
  field: FormField;
}

export const LayoutPanel = ({ field }: LayoutPanelProps) => {
  const { updateField, bringToFront, bringForward, sendBackward, sendToBack } =
    useTemplateContext();

  return (
    <AccordionItem value="layout">
      <AccordionTrigger>Layout & Ordering</AccordionTrigger>
      <AccordionContent className="space-y-4 pt-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="width">Width</Label>
            <Input
              id="width"
              type="number"
              value={field.width}
              onChange={(e) =>
                updateField(field.id, {
                  width: parseInt(e.target.value) || 0,
                })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="height">Height</Label>
            <Input
              id="height"
              type="number"
              value={field.height}
              onChange={(e) =>
                updateField(field.id, {
                  height: parseInt(e.target.value) || 0,
                })
              }
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label>Layer Order</Label>
          <div className="grid grid-cols-4 gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => bringToFront(field.id)}
                >
                  <ChevronsUp className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Bring to Front (Cmd/Ctrl+Shift+])</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => bringForward(field.id)}
                >
                  <ArrowUp className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Bring Forward (Cmd/Ctrl+])</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => sendBackward(field.id)}
                >
                  <ArrowDown className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Send Backward (Cmd/Ctrl+[)</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => sendToBack(field.id)}
                >
                  <ChevronsDown className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Send to Back (Cmd/Ctrl+Shift+[)</TooltipContent>
            </Tooltip>
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};