import React, { useState, useMemo, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { showError } from "@/utils/toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { getUniqueCategories, Template } from "@/data/templates";

const NEW_ITEM_VALUE = "__new__";
const NONE_ITEM_VALUE = "__none__";

interface EditTemplateDialogProps {
  template: Template | null;
  onClose: () => void;
  onSave: (
    originalTitle: string,
    updatedDetails: {
      title: string;
      category: string;
      subcategory?: string;
    }
  ) => void;
}

export const EditTemplateDialog: React.FC<EditTemplateDialogProps> = ({
  template,
  onClose,
  onSave,
}) => {
  const [title, setTitle] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [newCategory, setNewCategory] = useState("");
  const [selectedSubcategory, setSelectedSubcategory] = useState(NONE_ITEM_VALUE);
  const [newSubcategory, setNewSubcategory] = useState("");

  const categoriesData = useMemo(() => getUniqueCategories(), [template]);

  const subcategoriesForSelectedCategory = useMemo(() => {
    const categoryData = categoriesData.find(
      (c) => c.category === selectedCategory
    );
    return categoryData ? categoryData.subcategories : [];
  }, [selectedCategory, categoriesData]);

  useEffect(() => {
    if (template) {
      setTitle(template.title);
      setSelectedCategory(template.category);
      setSelectedSubcategory(template.subcategory || NONE_ITEM_VALUE);
      setNewCategory("");
      setNewSubcategory("");
    }
  }, [template]);

  const handleSave = (e: React.FormEvent) => {
    e.preventDefault();
    if (!template) return;
    if (!title.trim()) {
      showError("Please enter a title.");
      return;
    }

    const finalCategory =
      selectedCategory === NEW_ITEM_VALUE
        ? newCategory.trim()
        : selectedCategory;
    if (!finalCategory) {
      showError("Please select or create a category.");
      return;
    }

    const finalSubcategory =
      selectedSubcategory === NEW_ITEM_VALUE
        ? newSubcategory.trim()
        : selectedSubcategory === NONE_ITEM_VALUE
        ? ""
        : selectedSubcategory;

    onSave(template.title, {
      title: title.trim(),
      category: finalCategory,
      subcategory: finalSubcategory || undefined,
    });
  };

  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    setSelectedSubcategory(NONE_ITEM_VALUE);
    setNewSubcategory("");
  };

  return (
    <Dialog open={!!template} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Template Details</DialogTitle>
          <DialogDescription>
            Update the title and categories for your template.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSave}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Template Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select value={selectedCategory} onValueChange={handleCategoryChange}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select a category..." />
                </SelectTrigger>
                <SelectContent>
                  {categoriesData.map((c) => (
                    <SelectItem key={c.category} value={c.category}>
                      {c.category}
                    </SelectItem>
                  ))}
                  <SelectItem value={NEW_ITEM_VALUE}>
                    Create a new category...
                  </SelectItem>
                </SelectContent>
              </Select>
              {selectedCategory === NEW_ITEM_VALUE && (
                <Input
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  placeholder="New category name"
                  className="mt-2"
                />
              )}
            </div>
            {selectedCategory && selectedCategory !== NEW_ITEM_VALUE && (
              <div className="space-y-2">
                <Label htmlFor="subcategory">Subcategory (Optional)</Label>
                <Select
                  value={selectedSubcategory}
                  onValueChange={setSelectedSubcategory}
                >
                  <SelectTrigger id="subcategory">
                    <SelectValue placeholder="Select a subcategory..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={NONE_ITEM_VALUE}>None</SelectItem>
                    {subcategoriesForSelectedCategory.map((sc) => (
                      <SelectItem key={sc} value={sc}>
                        {sc}
                      </SelectItem>
                    ))}
                    <SelectItem value={NEW_ITEM_VALUE}>
                      Create a new subcategory...
                    </SelectItem>
                  </SelectContent>
                </Select>
                {selectedSubcategory === NEW_ITEM_VALUE && (
                  <Input
                    value={newSubcategory}
                    onChange={(e) => setNewSubcategory(e.target.value)}
                    placeholder="New subcategory name"
                    className="mt-2"
                  />
                )}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};