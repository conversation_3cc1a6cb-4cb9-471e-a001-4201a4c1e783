import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle } from "lucide-react";

interface CreateTemplateCardProps {
  onClick: () => void;
}

export const CreateTemplateCard: React.FC<CreateTemplateCardProps> = ({ onClick }) => {
  return (
    <Card
      className="overflow-hidden h-full flex flex-col group cursor-pointer transition-all duration-300 hover:shadow-xl border-2 border-dashed hover:border-primary"
      onClick={onClick}
    >
      <CardContent className="p-4 flex-grow flex flex-col items-center justify-center text-center">
        <PlusCircle className="h-12 w-12 text-muted-foreground group-hover:text-primary transition-colors" />
        <h3 className="font-semibold mt-4">Create New Template</h3>
        <p className="text-sm text-muted-foreground mt-1">
          Start with a blank canvas.
        </p>
      </CardContent>
    </Card>
  );
};