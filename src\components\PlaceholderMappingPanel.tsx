import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Trash2, Edit3, Check, X, Eye, EyeOff } from 'lucide-react';
import { DetectedPlaceholder } from '@/utils/placeholderDetection';
import { FormField } from '@/data/templates';
import { cn } from '@/lib/utils';

interface PlaceholderMappingPanelProps {
  placeholders: DetectedPlaceholder[];
  onPlaceholderUpdate: (placeholderId: string, updates: Partial<DetectedPlaceholder>) => void;
  onPlaceholderDelete: (placeholderId: string) => void;
  onBulkRename: (pattern: string, replacement: string) => void;
  className?: string;
}

interface EditingPlaceholder {
  id: string;
  suggestedName: string;
  suggestedType: FormField['type'];
}

export const PlaceholderMappingPanel: React.FC<PlaceholderMappingPanelProps> = ({
  placeholders,
  onPlaceholderUpdate,
  onPlaceholderDelete,
  onBulkRename,
  className
}) => {
  const [editingPlaceholder, setEditingPlaceholder] = useState<EditingPlaceholder | null>(null);
  const [bulkRenamePattern, setBulkRenamePattern] = useState('');
  const [bulkRenameReplacement, setBulkRenameReplacement] = useState('');
  const [showBulkRename, setShowBulkRename] = useState(false);
  const [hiddenPlaceholders, setHiddenPlaceholders] = useState<Set<string>>(new Set());

  const fieldTypes: { value: FormField['type']; label: string }[] = [
    { value: 'text', label: 'Text' },
    { value: 'textarea', label: 'Text Area' },
    { value: 'date', label: 'Date' },
    { value: 'number', label: 'Number' },
    { value: 'email', label: 'Email' },
    { value: 'select', label: 'Dropdown' },
    { value: 'signature', label: 'Signature' }
  ];

  const handleStartEdit = (placeholder: DetectedPlaceholder) => {
    setEditingPlaceholder({
      id: placeholder.id,
      suggestedName: placeholder.suggestedName,
      suggestedType: placeholder.suggestedType
    });
  };

  const handleSaveEdit = () => {
    if (editingPlaceholder) {
      onPlaceholderUpdate(editingPlaceholder.id, {
        suggestedName: editingPlaceholder.suggestedName,
        suggestedType: editingPlaceholder.suggestedType
      });
      setEditingPlaceholder(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingPlaceholder(null);
  };

  const handleBulkRename = () => {
    if (bulkRenamePattern && bulkRenameReplacement) {
      onBulkRename(bulkRenamePattern, bulkRenameReplacement);
      setBulkRenamePattern('');
      setBulkRenameReplacement('');
      setShowBulkRename(false);
    }
  };

  const togglePlaceholderVisibility = (placeholderId: string) => {
    const newHidden = new Set(hiddenPlaceholders);
    if (newHidden.has(placeholderId)) {
      newHidden.delete(placeholderId);
    } else {
      newHidden.add(placeholderId);
    }
    setHiddenPlaceholders(newHidden);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getDetectionMethodBadge = (method: DetectedPlaceholder['detectionMethod']) => {
    const variants = {
      'explicit': 'default',
      'blank-line': 'secondary',
      'form-field': 'outline',
      'repeated-spaces': 'destructive'
    } as const;

    return (
      <Badge variant={variants[method] || 'secondary'} className="text-xs">
        {method.replace('-', ' ')}
      </Badge>
    );
  };

  const visiblePlaceholders = placeholders.filter(p => !hiddenPlaceholders.has(p.id));

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            Detected Placeholders ({placeholders.length})
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowBulkRename(!showBulkRename)}
          >
            Bulk Rename
          </Button>
        </div>
        
        {showBulkRename && (
          <div className="space-y-2 p-3 bg-muted rounded-lg">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="bulk-pattern" className="text-xs">Find Pattern</Label>
                <Input
                  id="bulk-pattern"
                  placeholder="e.g., field"
                  value={bulkRenamePattern}
                  onChange={(e) => setBulkRenamePattern(e.target.value)}
                  className="h-8"
                />
              </div>
              <div>
                <Label htmlFor="bulk-replacement" className="text-xs">Replace With</Label>
                <Input
                  id="bulk-replacement"
                  placeholder="e.g., input"
                  value={bulkRenameReplacement}
                  onChange={(e) => setBulkRenameReplacement(e.target.value)}
                  className="h-8"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button size="sm" onClick={handleBulkRename}>Apply</Button>
              <Button size="sm" variant="outline" onClick={() => setShowBulkRename(false)}>
                Cancel
              </Button>
            </div>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="flex-1 overflow-y-auto space-y-3">
        {visiblePlaceholders.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <p>No placeholders detected</p>
            <p className="text-sm mt-1">Upload a document to get started</p>
          </div>
        ) : (
          visiblePlaceholders.map((placeholder) => (
            <div
              key={placeholder.id}
              className="border rounded-lg p-3 space-y-3 hover:bg-accent/50 transition-colors"
            >
              {/* Header with confidence and detection method */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge className={cn("text-xs", getConfidenceColor(placeholder.confidence))}>
                    {Math.round(placeholder.confidence * 100)}%
                  </Badge>
                  {getDetectionMethodBadge(placeholder.detectionMethod)}
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => togglePlaceholderVisibility(placeholder.id)}
                    className="h-6 w-6 p-0"
                  >
                    {hiddenPlaceholders.has(placeholder.id) ? (
                      <EyeOff className="h-3 w-3" />
                    ) : (
                      <Eye className="h-3 w-3" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleStartEdit(placeholder)}
                    className="h-6 w-6 p-0"
                  >
                    <Edit3 className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onPlaceholderDelete(placeholder.id)}
                    className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              {/* Original text */}
              <div className="text-xs text-muted-foreground">
                <span className="font-medium">Original:</span> "{placeholder.text}"
              </div>

              {/* Editable fields */}
              {editingPlaceholder?.id === placeholder.id ? (
                <div className="space-y-2">
                  <div>
                    <Label htmlFor={`name-${placeholder.id}`} className="text-xs">Field Name</Label>
                    <Input
                      id={`name-${placeholder.id}`}
                      value={editingPlaceholder.suggestedName}
                      onChange={(e) => setEditingPlaceholder({
                        ...editingPlaceholder,
                        suggestedName: e.target.value
                      })}
                      className="h-8"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`type-${placeholder.id}`} className="text-xs">Field Type</Label>
                    <Select
                      value={editingPlaceholder.suggestedType}
                      onValueChange={(value: FormField['type']) => setEditingPlaceholder({
                        ...editingPlaceholder,
                        suggestedType: value
                      })}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {fieldTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveEdit} className="h-7">
                      <Check className="h-3 w-3 mr-1" />
                      Save
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleCancelEdit} className="h-7">
                      <X className="h-3 w-3 mr-1" />
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-1">
                  <div className="text-sm font-medium">{placeholder.suggestedName}</div>
                  <div className="text-xs text-muted-foreground">
                    Type: {fieldTypes.find(t => t.value === placeholder.suggestedType)?.label}
                  </div>
                </div>
              )}

              {/* Context preview */}
              {placeholder.context && (
                <div className="text-xs text-muted-foreground bg-muted p-2 rounded">
                  <span className="font-medium">Context:</span> {placeholder.context}
                </div>
              )}
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
};
