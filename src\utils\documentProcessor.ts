import { PlaceholderDetectionEngine, DetectedPlaceholder, PlaceholderDetectionResult } from './placeholderDetection';
import { TemplateAutoGenerator, AutoGenerationOptions, GenerationResult } from './templateAutoGenerator';
import { SecurityValidator, SecurityValidationResult } from './securityValidator';
import { FormField, Template, Page } from '@/data/templates';

export interface ProcessedDocument {
  template: Template;
  originalFile: File;
  detectionResult: PlaceholderDetectionResult;
  generationResult?: GenerationResult;
  securityValidation?: SecurityValidationResult;
  processingStatus: 'processing' | 'completed' | 'failed';
  error?: string;
}

export interface DocumentProcessingOptions {
  preserveLayout: boolean;
  autoGenerateFields: boolean;
  pageFormat: 'letter' | 'a4' | 'legal';
  pageOrientation: 'portrait' | 'landscape';
}

/**
 * Document processing pipeline for template upload and conversion
 */
export class DocumentProcessor {
  private detectionEngine: PlaceholderDetectionEngine;
  private autoGenerator: TemplateAutoGenerator;
  private securityValidator: SecurityValidator;

  constructor() {
    this.detectionEngine = new PlaceholderDetectionEngine();
    this.autoGenerator = new TemplateAutoGenerator();
    this.securityValidator = new SecurityValidator();
  }

  /**
   * Process uploaded document and convert to template
   */
  async processDocument(
    file: File, 
    templateName: string,
    options: DocumentProcessingOptions = {
      preserveLayout: true,
      autoGenerateFields: true,
      pageFormat: 'letter',
      pageOrientation: 'portrait'
    }
  ): Promise<ProcessedDocument> {
    try {
      // Security validation first
      const securityValidation = await this.securityValidator.validateFile(file, {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedMimeTypes: [
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/pdf'
        ],
        allowedExtensions: ['.docx', '.pdf'],
        scanForMalware: true,
        sanitizeContent: true,
        checkFileIntegrity: true
      });

      if (!securityValidation.isValid) {
        throw new Error(`Security validation failed: ${securityValidation.errors.join(', ')}`);
      }

      // Basic file validation (legacy)
      this.validateFile(file);

      // Detect placeholders
      const detectionResult = await this.detectionEngine.detectPlaceholders(file);

      // Auto-generate template using the new system
      const generationResult = await this.autoGenerator.generateTemplate(
        templateName,
        detectionResult,
        file,
        {
          preserveOriginalLayout: options.preserveLayout,
          autoPositioning: options.autoGenerateFields,
          pageMargins: {
            top: 60,
            right: 40,
            bottom: 60,
            left: 40
          }
        }
      );

      return {
        template: generationResult.template,
        originalFile: file,
        detectionResult,
        generationResult,
        securityValidation,
        processingStatus: 'completed'
      };
    } catch (error) {
      console.error('Document processing failed:', error);
      return {
        template: this.createEmptyTemplate(templateName),
        originalFile: file,
        detectionResult: {
          placeholders: [],
          totalDetected: 0,
          detectionMethod: 'auto-detected',
          documentText: '',
          documentStructure: {}
        },
        generationResult: undefined,
        processingStatus: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Validate uploaded file
   */
  private validateFile(file: File): void {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/pdf'
    ];
    const allowedExtensions = ['.docx', '.pdf'];

    if (file.size > maxSize) {
      throw new Error('File size exceeds 10MB limit');
    }

    const hasValidType = allowedTypes.includes(file.type);
    const hasValidExtension = allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));

    if (!hasValidType && !hasValidExtension) {
      throw new Error('Invalid file type. Please upload a DOCX or PDF file');
    }

    // Additional security checks
    if (file.name.includes('../') || file.name.includes('..\\')) {
      throw new Error('Invalid file name');
    }
  }

  /**
   * Convert detection result to template format
   */
  private async convertToTemplate(
    templateName: string,
    detectionResult: PlaceholderDetectionResult,
    options: DocumentProcessingOptions
  ): Promise<Template> {
    const fields = this.convertPlaceholdersToFields(detectionResult.placeholders);
    
    // Create page with fields
    const page: Page = {
      id: `page_${Date.now()}`,
      fields
    };

    // Determine template category based on content
    const category = this.determineTemplateCategory(detectionResult.documentText);

    const template: Template = {
      title: templateName,
      description: `Template generated from uploaded document (${detectionResult.totalDetected} fields detected)`,
      category,
      subcategory: this.determineSubcategory(detectionResult.documentText, category),
      pages: [page],
      pageSetup: {
        format: options.pageFormat,
        orientation: options.pageOrientation
      },
      designSettings: {
        primaryColor: "#111827",
        textColor: "#374151",
        accentColor: "#e5e7eb",
        fontFamily: "Helvetica",
        showTitle: true
      },
      isCustom: true
    };

    return template;
  }

  /**
   * Convert detected placeholders to form fields
   */
  private convertPlaceholdersToFields(placeholders: DetectedPlaceholder[]): FormField[] {
    const fields: FormField[] = [];
    let yPosition = 100; // Start position
    const fieldHeight = 30;
    const fieldSpacing = 40;

    placeholders.forEach((placeholder, index) => {
      const field: FormField = {
        id: placeholder.id,
        label: this.formatFieldLabel(placeholder.suggestedName),
        type: placeholder.suggestedType,
        x: 40, // Left margin
        y: yPosition,
        width: this.getFieldWidth(placeholder.suggestedType),
        height: this.getFieldHeight(placeholder.suggestedType),
        placeholder: this.generatePlaceholderText(placeholder.suggestedName, placeholder.suggestedType),
        validation: this.generateValidationRules(placeholder.suggestedType)
      };

      // Add options for select fields if we can infer them
      if (placeholder.suggestedType === 'select') {
        field.options = this.generateSelectOptions(placeholder.context);
      }

      fields.push(field);
      yPosition += fieldHeight + fieldSpacing;
    });

    return fields;
  }

  /**
   * Format field label for display
   */
  private formatFieldLabel(suggestedName: string): string {
    return suggestedName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  /**
   * Get appropriate field width based on type
   */
  private getFieldWidth(type: FormField['type']): number {
    switch (type) {
      case 'textarea': return 400;
      case 'signature': return 300;
      case 'date': return 150;
      case 'number': return 120;
      case 'email': return 250;
      default: return 200;
    }
  }

  /**
   * Get appropriate field height based on type
   */
  private getFieldHeight(type: FormField['type']): number {
    switch (type) {
      case 'textarea': return 80;
      case 'signature': return 100;
      default: return 30;
    }
  }

  /**
   * Generate placeholder text for field
   */
  private generatePlaceholderText(suggestedName: string, type: FormField['type']): string {
    const formattedName = this.formatFieldLabel(suggestedName);
    
    switch (type) {
      case 'date': return 'Select date...';
      case 'number': return 'Enter number...';
      case 'email': return 'Enter email address...';
      case 'textarea': return `Enter ${formattedName.toLowerCase()}...`;
      case 'signature': return 'Click to sign...';
      case 'select': return 'Select option...';
      default: return `Enter ${formattedName.toLowerCase()}...`;
    }
  }

  /**
   * Generate validation rules based on field type
   */
  private generateValidationRules(type: FormField['type']): FormField['validation'] {
    switch (type) {
      case 'email':
        return { required: true };
      case 'number':
        return { required: false, minValue: 0 };
      case 'textarea':
        return { maxLength: 1000 };
      case 'text':
        return { maxLength: 255 };
      default:
        return {};
    }
  }

  /**
   * Generate select options from context
   */
  private generateSelectOptions(context: string): string[] {
    // Look for common railway options in context
    const railwayOptions = {
      division: ['Northern Railway', 'Southern Railway', 'Eastern Railway', 'Western Railway', 'Central Railway'],
      status: ['Active', 'Inactive', 'Under Maintenance', 'Out of Service'],
      priority: ['High', 'Medium', 'Low'],
      type: ['Passenger', 'Freight', 'Express', 'Local']
    };

    const lowerContext = context.toLowerCase();
    
    for (const [key, options] of Object.entries(railwayOptions)) {
      if (lowerContext.includes(key)) {
        return options;
      }
    }

    return ['Option 1', 'Option 2', 'Option 3'];
  }

  /**
   * Determine template category based on document content
   */
  private determineTemplateCategory(documentText: string): string {
    const lowerText = documentText.toLowerCase();
    
    if (lowerText.includes('joint report') || lowerText.includes('joint inspection')) {
      return 'Reports';
    }
    
    if (lowerText.includes('ta form') || lowerText.includes('travel allowance')) {
      return 'Forms';
    }
    
    if (lowerText.includes('invoice') || lowerText.includes('bill')) {
      return 'Invoices';
    }
    
    if (lowerText.includes('contract') || lowerText.includes('agreement')) {
      return 'Contracts';
    }
    
    return 'Custom';
  }

  /**
   * Determine template subcategory
   */
  private determineSubcategory(documentText: string, category: string): string | undefined {
    const lowerText = documentText.toLowerCase();
    
    if (category === 'Reports') {
      if (lowerText.includes('maintenance')) return 'Maintenance Reports';
      if (lowerText.includes('inspection')) return 'Inspection Reports';
      if (lowerText.includes('incident')) return 'Incident Reports';
      return 'General Reports';
    }
    
    if (category === 'Forms') {
      if (lowerText.includes('travel')) return 'Travel Forms';
      if (lowerText.includes('leave')) return 'Leave Forms';
      return 'General Forms';
    }
    
    return undefined;
  }

  /**
   * Create empty template as fallback
   */
  private createEmptyTemplate(templateName: string): Template {
    return {
      title: templateName,
      description: 'Empty template created due to processing error',
      category: 'Custom',
      pages: [{
        id: `page_${Date.now()}`,
        fields: []
      }],
      isCustom: true
    };
  }

  /**
   * Update placeholder mapping for a template
   */
  async updatePlaceholderMapping(
    template: Template,
    placeholderUpdates: { [placeholderId: string]: Partial<DetectedPlaceholder> }
  ): Promise<Template> {
    const updatedTemplate = { ...template };
    
    // Update fields based on placeholder changes
    updatedTemplate.pages = template.pages.map(page => ({
      ...page,
      fields: page.fields.map(field => {
        const update = placeholderUpdates[field.id];
        if (update) {
          return {
            ...field,
            label: update.suggestedName ? this.formatFieldLabel(update.suggestedName) : field.label,
            type: update.suggestedType || field.type,
            placeholder: update.suggestedName ? 
              this.generatePlaceholderText(update.suggestedName, update.suggestedType || field.type) : 
              field.placeholder
          };
        }
        return field;
      })
    }));

    return updatedTemplate;
  }
}
