import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useTemplateContext } from "@/context/TemplateContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { Trash2, Upload } from "lucide-react";
import { showError } from "@/utils/toast";
import { Slider } from "./ui/slider";

export const PageBackgroundPanel = () => {
  const { template, updatePageSettings } = useTemplateContext();
  const { activePageId } = useEditorUIContext();

  const activePage = template.pages.find(p => p.id === activePageId);
  const backgroundImage = activePage?.backgroundImage;

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!activePageId) return;
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { 
        showError("File is too large. Please upload an image under 5MB."); 
        return; 
      }
      const reader = new FileReader();
      reader.onloadend = () => {
        updatePageSettings(activePageId, { 
          backgroundImage: { 
            src: reader.result as string, 
            opacity: 1 
          } 
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleOpacityChange = (value: number[]) => {
    if (!activePageId || !backgroundImage) return;
    updatePageSettings(activePageId, {
      backgroundImage: { ...backgroundImage, opacity: value[0] }
    });
  };

  const removeBackgroundImage = () => {
    if (!activePageId) return;
    updatePageSettings(activePageId, { backgroundImage: undefined });
  };

  if (!activePage) {
    return null;
  }

  if (!backgroundImage) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Page Background</CardTitle>
          <CardDescription>Upload an image or PDF to use as a visual guide for placing fields.</CardDescription>
        </CardHeader>
        <CardContent>
          <Label htmlFor="bg-upload" className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-accent">
            <Upload className="w-8 h-8 text-muted-foreground" />
            <span className="mt-2 text-sm text-muted-foreground">Click to upload (PNG, JPG, PDF)</span>
          </Label>
          <Input id="bg-upload" type="file" className="sr-only" accept="image/png, image/jpeg, application/pdf" onChange={handleImageUpload} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Page Background</CardTitle>
        <CardDescription>Adjust the opacity of the background image or remove it.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-center border p-2 rounded-md bg-white">
          <img src={backgroundImage.src} alt="Background Preview" className="max-h-24 object-contain" />
        </div>
        <div className="space-y-2">
          <Label>Opacity ({Math.round(backgroundImage.opacity * 100)}%)</Label>
          <Slider
            value={[backgroundImage.opacity]}
            onValueChange={handleOpacityChange}
            min={0}
            max={1}
            step={0.05}
          />
        </div>
        <Button variant="destructive" className="w-full" onClick={removeBackgroundImage}>
          <Trash2 className="mr-2 h-4 w-4" />Remove Background
        </Button>
      </CardContent>
    </Card>
  );
};