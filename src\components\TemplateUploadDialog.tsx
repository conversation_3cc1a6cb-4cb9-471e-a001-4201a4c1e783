import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, FileText, Settings, Eye } from 'lucide-react';
import { TemplateUploadCard } from './TemplateUploadCard';
import { PlaceholderMappingPanel } from './PlaceholderMappingPanel';
import { DocumentProcessor, ProcessedDocument } from '@/utils/documentProcessor';
import { DetectedPlaceholder } from '@/utils/placeholderDetection';
import { Template } from '@/data/templates';
import { showError, showSuccess } from '@/utils/toast';

interface TemplateUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTemplateCreated: (template: Template) => void;
}

type UploadStep = 'upload' | 'configure' | 'preview' | 'complete';

export const TemplateUploadDialog: React.FC<TemplateUploadDialogProps> = ({
  open,
  onOpenChange,
  onTemplateCreated
}) => {
  const [currentStep, setCurrentStep] = useState<UploadStep>('upload');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedDocument, setProcessedDocument] = useState<ProcessedDocument | null>(null);
  const [templateName, setTemplateName] = useState('');
  const [templateCategory, setTemplateCategory] = useState('Custom');
  const [templateSubcategory, setTemplateSubcategory] = useState('');
  const [progress, setProgress] = useState(0);

  const documentProcessor = new DocumentProcessor();

  const categories = [
    'Reports',
    'Forms', 
    'Invoices',
    'Contracts',
    'Custom'
  ];

  const subcategories: Record<string, string[]> = {
    'Reports': ['Maintenance Reports', 'Inspection Reports', 'Incident Reports', 'General Reports'],
    'Forms': ['Travel Forms', 'Leave Forms', 'Application Forms', 'General Forms'],
    'Invoices': ['Service Invoices', 'Product Invoices', 'General Invoices'],
    'Contracts': ['Service Contracts', 'Employment Contracts', 'General Contracts'],
    'Custom': []
  };

  const handleFileUpload = async (file: File) => {
    setIsProcessing(true);
    setProgress(0);
    
    try {
      // Set initial template name from file
      const fileName = file.name.replace(/\.(docx|pdf)$/i, '');
      setTemplateName(fileName);
      
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const result = await documentProcessor.processDocument(file, fileName);
      
      clearInterval(progressInterval);
      setProgress(100);
      
      if (result.processingStatus === 'failed') {
        showError(result.error || 'Failed to process document');
        setIsProcessing(false);
        setProgress(0);
        return;
      }

      setProcessedDocument(result);
      setTemplateCategory(result.template.category);
      setTemplateSubcategory(result.template.subcategory || '');
      setCurrentStep('configure');
      
      showSuccess(`Document processed successfully! Found ${result.detectionResult.totalDetected} placeholders.`);
    } catch (error) {
      console.error('Upload error:', error);
      showError('Failed to process document. Please try again.');
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  };

  const handlePlaceholderUpdate = (placeholderId: string, updates: Partial<DetectedPlaceholder>) => {
    if (!processedDocument) return;

    const updatedPlaceholders = processedDocument.detectionResult.placeholders.map(p =>
      p.id === placeholderId ? { ...p, ...updates } : p
    );

    setProcessedDocument({
      ...processedDocument,
      detectionResult: {
        ...processedDocument.detectionResult,
        placeholders: updatedPlaceholders
      }
    });
  };

  const handlePlaceholderDelete = (placeholderId: string) => {
    if (!processedDocument) return;

    const updatedPlaceholders = processedDocument.detectionResult.placeholders.filter(
      p => p.id !== placeholderId
    );

    setProcessedDocument({
      ...processedDocument,
      detectionResult: {
        ...processedDocument.detectionResult,
        placeholders: updatedPlaceholders,
        totalDetected: updatedPlaceholders.length
      }
    });
  };

  const handleBulkRename = (pattern: string, replacement: string) => {
    if (!processedDocument) return;

    const updatedPlaceholders = processedDocument.detectionResult.placeholders.map(p => ({
      ...p,
      suggestedName: p.suggestedName.replace(new RegExp(pattern, 'gi'), replacement)
    }));

    setProcessedDocument({
      ...processedDocument,
      detectionResult: {
        ...processedDocument.detectionResult,
        placeholders: updatedPlaceholders
      }
    });
  };

  const handleCreateTemplate = async () => {
    if (!processedDocument) return;

    try {
      setIsProcessing(true);

      // Update template with user configurations
      const updatedTemplate: Template = {
        ...processedDocument.template,
        title: templateName,
        category: templateCategory,
        subcategory: templateSubcategory || undefined,
        description: `Template uploaded from ${processedDocument.originalFile.name} with ${processedDocument.detectionResult.totalDetected} detected fields`
      };

      // Apply placeholder updates to template
      const finalTemplate = await documentProcessor.updatePlaceholderMapping(
        updatedTemplate,
        Object.fromEntries(
          processedDocument.detectionResult.placeholders.map(p => [p.id, p])
        )
      );

      onTemplateCreated(finalTemplate);
      setCurrentStep('complete');
      
      showSuccess(`Template "${templateName}" created successfully!`);
    } catch (error) {
      console.error('Template creation error:', error);
      showError('Failed to create template. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    setCurrentStep('upload');
    setProcessedDocument(null);
    setTemplateName('');
    setTemplateCategory('Custom');
    setTemplateSubcategory('');
    setProgress(0);
    onOpenChange(false);
  };

  const getStepProgress = () => {
    switch (currentStep) {
      case 'upload': return 25;
      case 'configure': return 50;
      case 'preview': return 75;
      case 'complete': return 100;
      default: return 0;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Upload Template Document
          </DialogTitle>
          <DialogDescription>
            Upload a DOCX or PDF document to automatically generate a template with intelligent placeholder detection.
          </DialogDescription>
        </DialogHeader>

        {/* Progress indicator */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Step {currentStep === 'upload' ? 1 : currentStep === 'configure' ? 2 : currentStep === 'preview' ? 3 : 4} of 4</span>
            <span>{getStepProgress()}% complete</span>
          </div>
          <Progress value={isProcessing ? progress : getStepProgress()} className="h-2" />
        </div>

        <div className="flex-1 overflow-hidden">
          {currentStep === 'upload' && (
            <div className="h-full flex items-center justify-center">
              <div className="w-full max-w-md">
                <TemplateUploadCard
                  onUpload={handleFileUpload}
                  isProcessing={isProcessing}
                />
              </div>
            </div>
          )}

          {currentStep === 'configure' && processedDocument && (
            <div className="h-full flex flex-col space-y-4">
              {/* Template configuration */}
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="template-name">Template Name</Label>
                  <Input
                    id="template-name"
                    value={templateName}
                    onChange={(e) => setTemplateName(e.target.value)}
                    placeholder="Enter template name"
                  />
                </div>
                <div>
                  <Label htmlFor="template-category">Category</Label>
                  <Select value={templateCategory} onValueChange={setTemplateCategory}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="template-subcategory">Subcategory</Label>
                  <Select value={templateSubcategory} onValueChange={setTemplateSubcategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select subcategory" />
                    </SelectTrigger>
                    <SelectContent>
                      {subcategories[templateCategory]?.map(subcategory => (
                        <SelectItem key={subcategory} value={subcategory}>
                          {subcategory}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Detection results */}
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Successfully detected {processedDocument.detectionResult.totalDetected} placeholders using {processedDocument.detectionResult.detectionMethod} detection.
                </AlertDescription>
              </Alert>

              {/* Placeholder mapping */}
              <div className="flex-1 overflow-hidden">
                <PlaceholderMappingPanel
                  placeholders={processedDocument.detectionResult.placeholders}
                  onPlaceholderUpdate={handlePlaceholderUpdate}
                  onPlaceholderDelete={handlePlaceholderDelete}
                  onBulkRename={handleBulkRename}
                />
              </div>
            </div>
          )}

          {currentStep === 'complete' && (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
                <h3 className="text-xl font-semibold">Template Created Successfully!</h3>
                <p className="text-muted-foreground">
                  Your template "{templateName}" has been created and is ready to use.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer buttons */}
        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={handleClose}>
            {currentStep === 'complete' ? 'Close' : 'Cancel'}
          </Button>
          
          <div className="flex gap-2">
            {currentStep === 'configure' && (
              <>
                <Button variant="outline" onClick={() => setCurrentStep('upload')}>
                  Back
                </Button>
                <Button 
                  onClick={handleCreateTemplate}
                  disabled={!templateName.trim() || isProcessing}
                >
                  {isProcessing ? 'Creating...' : 'Create Template'}
                </Button>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
