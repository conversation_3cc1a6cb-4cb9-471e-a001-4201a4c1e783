import React from "react";
import { Rnd } from "react-rnd";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { cn } from "@/lib/utils";

interface GuideLineProps {
  orientation: "horizontal" | "vertical";
  position: number;
  index: number;
}

export const GuideLine: React.FC<GuideLineProps> = ({ orientation, position, index }) => {
  const { updateGuide, removeGuide } = useEditorUIContext();

  const handleDragStop = (e: any, data: { x: number; y: number }) => {
    const newPosition = orientation === "horizontal" ? data.y : data.x;
    
    if (newPosition < -20 || (orientation === 'horizontal' && newPosition > 792 + 20) || (orientation === 'vertical' && newPosition > 612 + 20)) {
      removeGuide(orientation, index);
    } else {
      updateGuide(orientation, index, Math.round(newPosition));
    }
  };

  const isHorizontal = orientation === 'horizontal';

  return (
    <Rnd
      position={isHorizontal ? { x: -20, y: position } : { x: position, y: -20 }}
      size={isHorizontal ? { width: 612 + 40, height: 1 } : { width: 1, height: 792 + 40 }}
      onDragStop={handleDragStop}
      enableResizing={false}
      bounds="parent"
      dragAxis={isHorizontal ? 'y' : 'x'}
      className={cn(
        "bg-cyan-500/75 z-20",
        isHorizontal ? 'cursor-row-resize' : 'cursor-col-resize'
      )}
    />
  );
};