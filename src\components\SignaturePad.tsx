import React, { useRef } from "react";
import SignatureCanvas from "react-signature-canvas";
import { Button } from "./ui/button";

interface SignaturePadProps {
  onSave: (signature: string) => void;
}

export const SignaturePad: React.FC<SignaturePadProps> = ({ onSave }) => {
  const sigPad = useRef<SignatureCanvas>(null);

  const clear = () => {
    sigPad.current?.clear();
  };

  const save = () => {
    const dataUrl = sigPad.current?.getTrimmedCanvas().toDataURL("image/png");
    if (dataUrl) {
      onSave(dataUrl);
    }
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="border rounded-md bg-white">
        <SignatureCanvas
          ref={sigPad}
          penColor="black"
          canvasProps={{
            width: 450,
            height: 200,
            className: "sigCanvas",
          }}
        />
      </div>
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={clear}>
          Clear
        </Button>
        <Button onClick={save}>Save</Button>
      </div>
    </div>
  );
};