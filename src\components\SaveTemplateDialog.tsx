import React, { useState, useMemo, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { useTemplateContext } from "@/context/TemplateContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { showError } from "@/utils/toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { getUniqueCategories } from "@/data/templates";

const NEW_ITEM_VALUE = "__new__";
const NONE_ITEM_VALUE = "__none__";

export const SaveTemplateDialog: React.FC = () => {
  const { saveAsTemplate, template } = useTemplateContext();
  const { isSaveDialogOpen, setIsSaveDialogOpen } = useEditorUIContext();

  const [title, setTitle] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [newCategory, setNewCategory] = useState("");
  const [selectedSubcategory, setSelectedSubcategory] = useState(NONE_ITEM_VALUE);
  const [newSubcategory, setNewSubcategory] = useState("");

  const categoriesData = useMemo(
    () => getUniqueCategories(),
    [isSaveDialogOpen]
  );

  const subcategoriesForSelectedCategory = useMemo(() => {
    const categoryData = categoriesData.find(
      (c) => c.category === selectedCategory
    );
    return categoryData ? categoryData.subcategories : [];
  }, [selectedCategory, categoriesData]);

  useEffect(() => {
    if (isSaveDialogOpen) {
      setTitle(`${template.title} (Copy)`);
      setSelectedCategory(template.category);
      setSelectedSubcategory(template.subcategory || NONE_ITEM_VALUE);
      setNewCategory("");
      setNewSubcategory("");
    }
  }, [isSaveDialogOpen, template]);

  const handleSave = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim()) {
      showError("Please enter a title.");
      return;
    }

    const finalCategory =
      selectedCategory === NEW_ITEM_VALUE
        ? newCategory.trim()
        : selectedCategory;
    if (!finalCategory) {
      showError("Please select or create a category.");
      return;
    }

    const finalSubcategory =
      selectedSubcategory === NEW_ITEM_VALUE
        ? newSubcategory.trim()
        : selectedSubcategory === NONE_ITEM_VALUE
        ? ""
        : selectedSubcategory;

    saveAsTemplate({
      title: title.trim(),
      category: finalCategory,
      subcategory: finalSubcategory || undefined,
    }, () => setIsSaveDialogOpen(false));
  };

  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    setSelectedSubcategory(NONE_ITEM_VALUE);
    setNewSubcategory("");
  };

  const showSubcategorySection =
    (selectedCategory && selectedCategory !== NEW_ITEM_VALUE) ||
    (selectedCategory === NEW_ITEM_VALUE && newCategory.trim() !== "");

  return (
    <Dialog open={isSaveDialogOpen} onOpenChange={setIsSaveDialogOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Save as New Template</DialogTitle>
          <DialogDescription>
            Save your customizations as a new, reusable template with organized
            categories.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSave}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Template Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="e.g., My Custom Invoice"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={selectedCategory}
                onValueChange={handleCategoryChange}
              >
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select a category..." />
                </SelectTrigger>
                <SelectContent>
                  {categoriesData.map((c) => (
                    <SelectItem key={c.category} value={c.category}>
                      {c.category}
                    </SelectItem>
                  ))}
                  <SelectItem value={NEW_ITEM_VALUE}>
                    Create a new category...
                  </SelectItem>
                </SelectContent>
              </Select>
              {selectedCategory === NEW_ITEM_VALUE && (
                <Input
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  placeholder="New category name"
                  className="mt-2"
                />
              )}
            </div>
            {showSubcategorySection && (
              <div className="space-y-2">
                <Label htmlFor="subcategory">Subcategory (Optional)</Label>
                <Select
                  value={selectedSubcategory}
                  onValueChange={setSelectedSubcategory}
                >
                  <SelectTrigger id="subcategory">
                    <SelectValue placeholder="Select a subcategory..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={NONE_ITEM_VALUE}>None</SelectItem>
                    {subcategoriesForSelectedCategory.map((sc) => (
                      <SelectItem key={sc} value={sc}>
                        {sc}
                      </SelectItem>
                    ))}
                    <SelectItem value={NEW_ITEM_VALUE}>
                      Create a new subcategory...
                    </SelectItem>
                  </SelectContent>
                </Select>
                {selectedSubcategory === NEW_ITEM_VALUE && (
                  <Input
                    value={newSubcategory}
                    onChange={(e) => setNewSubcategory(e.target.value)}
                    placeholder="New subcategory name"
                    className="mt-2"
                  />
                )}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button type="submit">Save Template</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};