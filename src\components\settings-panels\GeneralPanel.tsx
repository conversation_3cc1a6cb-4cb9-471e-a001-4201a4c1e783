import { FormField } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface GeneralPanelProps {
  field: FormField;
}

export const GeneralPanel = ({ field }: GeneralPanelProps) => {
  const { updateField } = useTemplateContext();

  return (
    <AccordionItem value="general">
      <AccordionTrigger>General</AccordionTrigger>
      <AccordionContent className="space-y-4 pt-4">
        <div className="space-y-2">
          <Label htmlFor="label">Label</Label>
          <Input
            id="label"
            value={field.label}
            onChange={(e) => updateField(field.id, { label: e.target.value })}
          />
        </div>

        {["text", "textarea", "number", "email", "date", "select", "checkbox"].includes(field.type) && (
          <div className="space-y-2">
            <Label htmlFor="placeholder">Placeholder / Helper Text</Label>
            <Input
              id="placeholder"
              value={field.placeholder || ""}
              onChange={(e) =>
                updateField(field.id, { placeholder: e.target.value })
              }
            />
          </div>
        )}

        {field.type === "checkbox" ? (
          <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
            <div className="space-y-0.5">
              <Label>Checked by Default</Label>
            </div>
            <Switch
              checked={!!field.defaultValue}
              onCheckedChange={(c) =>
                updateField(field.id, { defaultValue: c })
              }
            />
          </div>
        ) : (
          ["text", "textarea", "number", "email", "date", "select", "radio"].includes(field.type) && (
            <div className="space-y-2">
              <Label htmlFor="defaultValue">Default Value</Label>
              <Input
                id="defaultValue"
                value={(field.defaultValue as string) || ""}
                onChange={(e) =>
                  updateField(field.id, { defaultValue: e.target.value })
                }
              />
              {(field.type === "select" || field.type === "radio") && (
                <p className="text-xs text-muted-foreground">
                  Must match one of the options exactly.
                </p>
              )}
            </div>
          )
        )}
      </AccordionContent>
    </AccordionItem>
  );
};