"use client"

import * as React from "react"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  PieChart,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  RadialBar,
  RadialBarChart,
  Rectangle,
  ResponsiveContainer,
  Scatter,
  ScatterChart,
  Sector,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"
import type { Props as SectorProps } from "recharts/types/shape/Sector"

import { cn } from "@/lib/utils"

// Chart Container
const ChartContainer = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & {
    config: ChartConfig
    children: React.ReactElement
  }
>(({ id, className, children, config, ...props }, ref) => {
  const chartContainerId = `chart-container-${id}`
  const isMounted = React.useRef(false)

  React.useEffect(() => {
    isMounted.current = true
  }, [])

  return (
    <div
      data-chart
      ref={ref}
      id={chartContainerId}
      className={cn(
        "flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line]:stroke-border/50 [&_.recharts-polar-grid_[stroke=ccc]]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-sector_[stroke-width=4]]:stroke-background [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",
        className
      )}
      {...props}
    >
      <ChartContext.Provider value={{ config }}>
        {isMounted.current ? (
          <ResponsiveContainer width="100%" height="100%">
            {children}
          </ResponsiveContainer>
        ) : null}
      </ChartContext.Provider>
    </div>
  )
})
ChartContainer.displayName = "Chart"

// Chart Context
type ChartConfig = {
  [k in string]: {
    label?: React.ReactNode
    icon?: React.ComponentType
  } & ({ color?: string; theme?: never } | { color?: never; theme: object })
}

const ChartContext = React.createContext<{
  config: ChartConfig
}>({
  config: {},
})

function useChart() {
  const context = React.useContext(ChartContext)

  if (!context) {
    throw new Error("useChart must be used within a <ChartContainer />")
  }

  return context
}

// Chart Tooltip
const ChartTooltip = Tooltip

type ChartTooltipContentProps = React.ComponentProps<"div"> & {
  active?: boolean
  formatter?: (
    value: any,
    name: any,
    item: any,
    index: number
  ) => React.ReactNode
  hideLabel?: boolean
  hideIndicator?: boolean
  indicator?: "line" | "dot" | "dashed"
  label?: React.ReactNode
  labelFormatter?: (label: any, payload: any[]) => React.ReactNode
  nameKey?: string
  payload?: any[]
  valueKey?: string
}

const ChartTooltipContent = React.forwardRef<
  HTMLDivElement,
  ChartTooltipContentProps
>(
  (
    {
      active,
      payload,
      className,
      indicator = "dot",
      hideLabel = false,
      hideIndicator = false,
      label,
      labelFormatter,
      formatter,
      valueKey = "value",
      nameKey = "name",
    },
    ref
  ) => {
    const { config } = useChart()

    const tooltipLabel = React.useMemo(() => {
      if (hideLabel || !payload?.length) {
        return null
      }

      if (labelFormatter) {
        return labelFormatter(label, payload)
      }

      return label
    }, [label, payload, hideLabel, labelFormatter])

    if (!active || !payload?.length) {
      return null
    }

    const nestLabel = payload.length === 1 && payload[0].name === valueKey

    return (
      <div
        ref={ref}
        className={cn(
          "grid min-w-[8rem] items-stretch gap-1.5 rounded-md border bg-background p-2.5 text-xs shadow-xl animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
          className
        )}
      >
        {!hideLabel && tooltipLabel ? (
          <div className="font-medium">{tooltipLabel}</div>
        ) : null}
        <div className="grid gap-1.5">
          {payload.map((item, index) => {
            const key = `${item.name || item.dataKey || "value"}`
            const itemConfig = config[key]
            const indicatorColor = item.color

            return (
              <div
                key={item.dataKey}
                className={cn(
                  "flex w-full items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground",
                  hideIndicator && "items-center"
                )}
              >
                {formatter ? (
                  formatter(item.value, item.name, item, index)
                ) : (
                  <>
                    {!hideIndicator && (
                      <div
                        className={cn(
                          "shrink-0 rounded-[2px] border-[1px] border-solid",
                          {
                            "bg-[--color-indicator] border-[--color-indicator]":
                              indicatorColor,
                            "bg-transparent": !indicatorColor,
                          }
                        )}
                        style={
                          {
                            "--color-indicator": indicatorColor,
                          } as React.CSSProperties
                        }
                      >
                        {itemConfig?.icon ? (
                          <itemConfig.icon />
                        ) : (
                          <div
                            className={cn("h-full w-full", {
                              "rounded-full": indicator === "dot",
                              "border-2 border-dashed bg-background":
                                indicator === "dashed",
                              "w-0.5": indicator === "line",
                            })}
                          />
                        )}
                      </div>
                    )}
                    <div
                      className={cn(
                        "flex flex-1 justify-between leading-none",
                        nestLabel && "items-center"
                      )}
                    >
                      <div className="grid gap-1.5">
                        {nestLabel ? null : (
                          <span className="text-muted-foreground">
                            {itemConfig?.label || item.name}
                          </span>
                        )}
                        <span className="font-medium">
                          {item.value.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </>
                )}
              </div>
            )
          })}
        </div>
      </div>
    )
  }
)
ChartTooltipContent.displayName = "ChartTooltipContent"

// Chart Legend
const ChartLegend = Legend

type ChartLegendContentProps = React.ComponentProps<"div"> & {
  hideIcon?: boolean
  nameKey?: string
  payload?: any[]
  verticalAlign?: "top" | "middle" | "bottom"
}

const ChartLegendContent = React.forwardRef<
  HTMLDivElement,
  ChartLegendContentProps
>(({ className, hideIcon = false, payload, verticalAlign, nameKey }, ref) => {
  const { config } = useChart()

  if (!payload?.length) {
    return null
  }

  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-center gap-4",
        verticalAlign === "top" ? "pb-4" : "pt-4",
        className
      )}
    >
      {payload.map((item) => {
        const key = `${nameKey || item.dataKey || "value"}`
        const itemConfig = config[key]

        return (
          <div
            key={item.value}
            className={cn(
              "flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"
            )}
          >
            {itemConfig?.icon && !hideIcon ? (
              <itemConfig.icon />
            ) : (
              <div
                className="h-2 w-2 shrink-0 rounded-[2px]"
                style={{
                  backgroundColor: item.color,
                }}
              />
            )}
            {itemConfig?.label}
          </div>
        )
      })}
    </div>
  )
})
ChartLegendContent.displayName = "ChartLegendContent"

// Exports
const Chart = Object.assign(ChartContainer, {
  AreaChart,
  BarChart,
  LineChart,
  PieChart,
  RadarChart,
  RadialBarChart,
  ScatterChart,
  // Components
  CartesianGrid,
  Tooltip: ChartTooltip,
  TooltipContent: ChartTooltipContent,
  Legend: ChartLegend,
  LegendContent: ChartLegendContent,
  XAxis,
  YAxis,
  // Primitives
  Area,
  Bar,
  Line,
  Pie,
  Radar,
  RadialBar,
  Scatter,
  Cell,
  Rectangle,
  Sector,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
})

export { Chart, type ChartConfig }