import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Page, Template } from "@/data/templates";
import { PdfPreview } from "./PdfPreview";
import { cn } from "@/lib/utils";
import { GripVertical, Copy, Trash2 } from "lucide-react";
import { Button } from "./ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface SortablePageThumbnailProps {
  page: Page;
  template: Template;
  pageIndex: number;
  isActive: boolean;
  onClick: () => void;
  onDuplicate: (pageId: string) => void;
  onDelete: (pageId: string) => void;
  isDeleteDisabled: boolean;
}

export const SortablePageThumbnail: React.FC<SortablePageThumbnailProps> = ({
  page,
  template,
  pageIndex,
  isActive,
  onClick,
  onDuplicate,
  onDelete,
  isDeleteDisabled,
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: page.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "p-2 rounded-md border-2 flex items-center gap-2 cursor-pointer group",
        isActive ? "border-primary bg-primary/10" : "border-transparent hover:bg-accent"
      )}
      onClick={onClick}
    >
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8 cursor-grab"
        {...attributes}
        {...listeners}
        onClick={(e) => e.stopPropagation()}
      >
        <GripVertical className="h-5 w-5 text-muted-foreground" />
      </Button>
      <div className="w-16 h-[82px] bg-white shadow-sm overflow-hidden border">
        <div style={{ transform: "scale(0.1045)", transformOrigin: "top left" }}>
          <PdfPreview template={template} page={page} pageIndex={pageIndex} variant="card" />
        </div>
      </div>
      <div className="flex-1">
        <p className="text-sm font-medium">Page {pageIndex + 1}</p>
      </div>
      <div className="flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          variant="ghost"
          size="icon"
          className="h-7 w-7"
          onClick={(e) => { e.stopPropagation(); onDuplicate(page.id); }}
        >
          <Copy className="h-4 w-4" />
        </Button>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-destructive hover:text-destructive"
              disabled={isDeleteDisabled}
              onClick={(e) => e.stopPropagation()}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent onClick={(e) => e.stopPropagation()}>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Page {pageIndex + 1}?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete this page and all its fields. This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={() => onDelete(page.id)}>
                Delete Page
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};