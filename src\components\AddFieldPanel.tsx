import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  Card<PERSON><PERSON>er,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { DraggableFieldTool } from "./DraggableFieldTool";
import {
  Type,
  MessageSquare,
  Calendar,
  MousePointerClick,
  Hash,
  Mail,
  CheckSquare,
  Table,
  Image,
  ListChecks,
  Square,
  Minus,
  ArrowRight,
  QrCode,
} from "lucide-react";
import { FormField } from "@/data/templates";

const fieldTools: {
  type: FormField["type"];
  label: string;
  icon: React.ElementType;
}[] = [
  { type: "text", label: "Text", icon: Type },
  { type: "textarea", label: "Text Area", icon: MessageSquare },
  { type: "date", label: "Date", icon: Calendar },
  { type: "signature", label: "Signature", icon: MousePointerClick },
  { type: "number", label: "Number", icon: Hash },
  { type: "email", label: "Email", icon: Mail },
  { type: "checkbox", label: "Checkbox", icon: CheckSquare },
  { type: "select", label: "Select", icon: CheckSquare },
  { type: "radio", label: "Radio Group", icon: ListChecks },
  { type: "static-text", label: "Static Text", icon: Type },
  { type: "table", label: "Table", icon: Table },
  { type: "image", label: "Image", icon: Image },
  { type: "qrcode", label: "QR Code", icon: QrCode },
  { type: "box", label: "Box", icon: Square },
  { type: "line", label: "Line", icon: Minus },
  { type: "arrow", label: "Arrow", icon: ArrowRight },
];

export const AddFieldPanel = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Add a New Field</CardTitle>
        <CardDescription>
          Drag and drop a field onto the document preview to add it.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-2">
          {fieldTools.map((tool) => (
            <DraggableFieldTool
              key={tool.type}
              type={tool.type}
              label={tool.label}
              icon={tool.icon}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};