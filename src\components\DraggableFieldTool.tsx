import React from "react";
import { useDraggable } from "@dnd-kit/core";
import { cn } from "@/lib/utils";
import { FormField } from "@/data/templates";

interface DraggableFieldToolProps {
  type: FormField["type"];
  label: string;
  icon: React.ElementType;
}

export const DraggableFieldTool: React.FC<DraggableFieldToolProps> = ({
  type,
  label,
  icon: Icon,
}) => {
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: `tool-${type}`,
    data: { type, label, icon: Icon },
  });

  return (
    <button
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      className={cn(
        "flex flex-col items-center justify-center gap-2 p-2 border rounded-md hover:bg-accent hover:shadow-md transition-all text-center w-full h-20",
        isDragging && "opacity-25"
      )}
    >
      <Icon className="h-6 w-6" />
      <span className="text-xs">{label}</span>
    </button>
  );
};