import { Template, <PERSON><PERSON><PERSON>, <PERSON>, DesignSettings, PageSetup } from '@/data/templates';
import { DetectedPlaceholder, PlaceholderDetectionResult } from './placeholderDetection';
import { PlaceholderManager } from './placeholderManager';

export interface AutoGenerationOptions {
  preserveOriginalLayout: boolean;
  autoPositioning: boolean;
  fieldSpacing: number;
  pageMargins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  groupRelatedFields: boolean;
  generateValidation: boolean;
}

export interface GenerationResult {
  template: Template;
  placeholderManager: PlaceholderManager;
  statistics: {
    totalPlaceholders: number;
    fieldsGenerated: number;
    groupsCreated: number;
    validationRulesApplied: number;
  };
  warnings: string[];
}

/**
 * Auto-generates templates from detected placeholders with intelligent field positioning
 */
export class TemplateAutoGenerator {
  private defaultOptions: AutoGenerationOptions = {
    preserveOriginalLayout: true,
    autoPositioning: true,
    fieldSpacing: 40,
    pageMargins: {
      top: 60,
      right: 40,
      bottom: 60,
      left: 40
    },
    groupRelatedFields: true,
    generateValidation: true
  };

  /**
   * Generate template from placeholder detection result
   */
  async generateTemplate(
    templateName: string,
    detectionResult: PlaceholderDetectionResult,
    originalFile: File,
    options: Partial<AutoGenerationOptions> = {}
  ): Promise<GenerationResult> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const warnings: string[] = [];
    
    try {
      // Create base template structure
      const baseTemplate = this.createBaseTemplate(templateName, detectionResult, originalFile);
      
      // Initialize placeholder manager
      const placeholderManager = new PlaceholderManager(baseTemplate);
      
      // Process placeholders and generate fields
      const { fields, groups, validationCount } = await this.processPlaceholders(
        detectionResult.placeholders,
        mergedOptions,
        warnings
      );
      
      // Create pages with generated fields
      const pages = this.createPages(fields, groups, mergedOptions);
      
      // Finalize template
      const finalTemplate: Template = {
        ...baseTemplate,
        pages,
        description: this.generateTemplateDescription(detectionResult, fields.length)
      };
      
      // Add placeholders to manager
      detectionResult.placeholders.forEach(placeholder => {
        const field = fields.find(f => f.id === placeholder.id);
        if (field) {
          placeholderManager.addPlaceholderMapping(placeholder, {
            x: field.x,
            y: field.y,
            width: field.width,
            height: field.height
          });
        }
      });
      
      return {
        template: finalTemplate,
        placeholderManager,
        statistics: {
          totalPlaceholders: detectionResult.placeholders.length,
          fieldsGenerated: fields.length,
          groupsCreated: groups.length,
          validationRulesApplied: validationCount
        },
        warnings
      };
    } catch (error) {
      console.error('Template auto-generation failed:', error);
      throw new Error(`Failed to generate template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create base template structure
   */
  private createBaseTemplate(
    templateName: string,
    detectionResult: PlaceholderDetectionResult,
    originalFile: File
  ): Template {
    const category = this.inferTemplateCategory(detectionResult.documentText);
    const subcategory = this.inferTemplateSubcategory(detectionResult.documentText, category);
    
    return {
      title: templateName,
      description: `Auto-generated template from ${originalFile.name}`,
      category,
      subcategory,
      pages: [],
      pageSetup: this.inferPageSetup(detectionResult.documentStructure),
      designSettings: this.createDesignSettings(),
      isCustom: true
    };
  }

  /**
   * Process placeholders and generate form fields
   */
  private async processPlaceholders(
    placeholders: DetectedPlaceholder[],
    options: AutoGenerationOptions,
    warnings: string[]
  ): Promise<{
    fields: FormField[];
    groups: FieldGroup[];
    validationCount: number;
  }> {
    const fields: FormField[] = [];
    const groups: FieldGroup[] = [];
    let validationCount = 0;
    let currentY = options.pageMargins.top;

    // Sort placeholders by confidence and position
    const sortedPlaceholders = this.sortPlaceholders(placeholders);
    
    // Group related placeholders if enabled
    const placeholderGroups = options.groupRelatedFields 
      ? this.groupRelatedPlaceholders(sortedPlaceholders)
      : [{ name: 'Default', placeholders: sortedPlaceholders }];

    for (const group of placeholderGroups) {
      const groupFields: FormField[] = [];
      
      // Add group header if there are multiple groups
      if (placeholderGroups.length > 1) {
        const headerField = this.createGroupHeader(group.name, options.pageMargins.left, currentY);
        fields.push(headerField);
        currentY += 40;
      }

      for (const placeholder of group.placeholders) {
        try {
          const field = await this.createFormField(placeholder, options, currentY);
          
          if (options.generateValidation && field.validation) {
            validationCount++;
          }
          
          fields.push(field);
          groupFields.push(field);
          currentY += field.height + options.fieldSpacing;
          
        } catch (error) {
          warnings.push(`Failed to create field for placeholder "${placeholder.text}": ${error}`);
        }
      }

      if (groupFields.length > 0) {
        groups.push({
          name: group.name,
          fields: groupFields,
          startY: currentY - (groupFields.length * (30 + options.fieldSpacing)),
          endY: currentY
        });
      }
      
      // Add spacing between groups
      currentY += 20;
    }

    return { fields, groups, validationCount };
  }

  /**
   * Create form field from detected placeholder
   */
  private async createFormField(
    placeholder: DetectedPlaceholder,
    options: AutoGenerationOptions,
    yPosition: number
  ): Promise<FormField> {
    const fieldWidth = this.calculateFieldWidth(placeholder.suggestedType);
    const fieldHeight = this.calculateFieldHeight(placeholder.suggestedType);
    
    const field: FormField = {
      id: placeholder.id,
      label: this.formatFieldLabel(placeholder.suggestedName),
      type: placeholder.suggestedType,
      x: options.pageMargins.left,
      y: yPosition,
      width: fieldWidth,
      height: fieldHeight,
      placeholder: this.generatePlaceholderText(placeholder.suggestedName, placeholder.suggestedType)
    };

    // Add validation rules if enabled
    if (options.generateValidation) {
      field.validation = this.generateValidationRules(placeholder);
    }

    // Add type-specific properties
    if (placeholder.suggestedType === 'select') {
      field.options = this.generateSelectOptions(placeholder.context);
    }

    // Add default values for certain field types
    if (placeholder.suggestedType === 'date') {
      field.defaultValue = '';
    }

    return field;
  }

  /**
   * Create pages with generated fields
   */
  private createPages(
    fields: FormField[],
    groups: FieldGroup[],
    options: AutoGenerationOptions
  ): Page[] {
    // For now, create a single page with all fields
    // In the future, this could be enhanced to split across multiple pages
    const page: Page = {
      id: `page_${Date.now()}`,
      fields
    };

    return [page];
  }

  /**
   * Sort placeholders by confidence and logical order
   */
  private sortPlaceholders(placeholders: DetectedPlaceholder[]): DetectedPlaceholder[] {
    return [...placeholders].sort((a, b) => {
      // First sort by detection method priority
      const methodPriority = {
        'explicit': 4,
        'form-field': 3,
        'blank-line': 2,
        'repeated-spaces': 1
      };
      
      const aPriority = methodPriority[a.detectionMethod] || 0;
      const bPriority = methodPriority[b.detectionMethod] || 0;
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }
      
      // Then sort by confidence
      return b.confidence - a.confidence;
    });
  }

  /**
   * Group related placeholders together
   */
  private groupRelatedPlaceholders(placeholders: DetectedPlaceholder[]): PlaceholderGroup[] {
    const groups: PlaceholderGroup[] = [];
    const ungrouped: DetectedPlaceholder[] = [];
    
    // Define grouping patterns
    const groupPatterns = [
      {
        name: 'Personal Information',
        keywords: ['name', 'email', 'phone', 'address', 'contact']
      },
      {
        name: 'Railway Details',
        keywords: ['loco', 'train', 'station', 'division', 'branch', 'track']
      },
      {
        name: 'Date & Time',
        keywords: ['date', 'time', 'day', 'month', 'year']
      },
      {
        name: 'Numbers & Codes',
        keywords: ['number', 'code', 'id', 'serial', 'count']
      },
      {
        name: 'Signatures & Approvals',
        keywords: ['signature', 'sign', 'approved', 'verified', 'checked']
      }
    ];

    // Group placeholders by patterns
    for (const pattern of groupPatterns) {
      const matchingPlaceholders = placeholders.filter(p => {
        const text = (p.suggestedName + ' ' + p.context).toLowerCase();
        return pattern.keywords.some(keyword => text.includes(keyword));
      });
      
      if (matchingPlaceholders.length > 0) {
        groups.push({
          name: pattern.name,
          placeholders: matchingPlaceholders
        });
        
        // Remove grouped placeholders from the main list
        matchingPlaceholders.forEach(p => {
          const index = ungrouped.indexOf(p);
          if (index === -1) {
            ungrouped.push(...placeholders.filter(ph => ph.id !== p.id));
          }
        });
      }
    }

    // Add remaining placeholders to a general group
    const remaining = placeholders.filter(p => 
      !groups.some(g => g.placeholders.some(gp => gp.id === p.id))
    );
    
    if (remaining.length > 0) {
      groups.push({
        name: 'Other Fields',
        placeholders: remaining
      });
    }

    return groups;
  }

  /**
   * Create group header field
   */
  private createGroupHeader(groupName: string, x: number, y: number): FormField {
    return {
      id: `header_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      label: groupName,
      type: 'static-text',
      x,
      y,
      width: 400,
      height: 25,
      defaultValue: groupName,
      fontSize: 16,
      fontWeight: 'bold',
      color: '#374151'
    };
  }

  // Helper methods for field generation
  private calculateFieldWidth(type: FormField['type']): number {
    const widths = {
      'textarea': 400,
      'signature': 300,
      'date': 150,
      'number': 120,
      'email': 250,
      'select': 200,
      'text': 200
    };
    return widths[type] || 200;
  }

  private calculateFieldHeight(type: FormField['type']): number {
    const heights = {
      'textarea': 80,
      'signature': 100,
      'static-text': 25
    };
    return heights[type] || 30;
  }

  private formatFieldLabel(name: string): string {
    return name
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  private generatePlaceholderText(name: string, type: FormField['type']): string {
    const formattedName = this.formatFieldLabel(name);
    
    const placeholders = {
      'date': 'Select date...',
      'number': 'Enter number...',
      'email': 'Enter email address...',
      'textarea': `Enter ${formattedName.toLowerCase()}...`,
      'signature': 'Click to sign...',
      'select': 'Select option...'
    };
    
    return placeholders[type] || `Enter ${formattedName.toLowerCase()}...`;
  }

  private generateValidationRules(placeholder: DetectedPlaceholder): FormField['validation'] {
    const rules: FormField['validation'] = {};
    
    // Add rules based on field type
    switch (placeholder.suggestedType) {
      case 'email':
        rules.required = true;
        break;
      case 'number':
        rules.required = false;
        rules.minValue = 0;
        break;
      case 'textarea':
        rules.maxLength = 1000;
        break;
      case 'text':
        rules.maxLength = 255;
        break;
    }

    // Add required validation for high-confidence explicit placeholders
    if (placeholder.detectionMethod === 'explicit' && placeholder.confidence > 0.8) {
      rules.required = true;
    }

    return Object.keys(rules).length > 0 ? rules : undefined;
  }

  private generateSelectOptions(context: string): string[] {
    const railwayOptions = {
      division: ['Northern Railway', 'Southern Railway', 'Eastern Railway', 'Western Railway', 'Central Railway'],
      status: ['Active', 'Inactive', 'Under Maintenance', 'Out of Service'],
      priority: ['High', 'Medium', 'Low'],
      type: ['Passenger', 'Freight', 'Express', 'Local']
    };

    const lowerContext = context.toLowerCase();
    
    for (const [key, options] of Object.entries(railwayOptions)) {
      if (lowerContext.includes(key)) {
        return options;
      }
    }

    return ['Option 1', 'Option 2', 'Option 3'];
  }

  private inferTemplateCategory(documentText: string): string {
    const lowerText = documentText.toLowerCase();
    
    if (lowerText.includes('joint report') || lowerText.includes('inspection')) {
      return 'Reports';
    }
    if (lowerText.includes('ta form') || lowerText.includes('travel allowance')) {
      return 'Forms';
    }
    if (lowerText.includes('invoice') || lowerText.includes('bill')) {
      return 'Invoices';
    }
    if (lowerText.includes('contract') || lowerText.includes('agreement')) {
      return 'Contracts';
    }
    
    return 'Custom';
  }

  private inferTemplateSubcategory(documentText: string, category: string): string | undefined {
    const lowerText = documentText.toLowerCase();
    
    if (category === 'Reports') {
      if (lowerText.includes('maintenance')) return 'Maintenance Reports';
      if (lowerText.includes('inspection')) return 'Inspection Reports';
      if (lowerText.includes('incident')) return 'Incident Reports';
    }
    
    return undefined;
  }

  private inferPageSetup(documentStructure: any): PageSetup {
    // Default to letter size, portrait orientation
    // Could be enhanced to detect from document structure
    return {
      format: 'letter',
      orientation: 'portrait'
    };
  }

  private createDesignSettings(): DesignSettings {
    return {
      primaryColor: "#111827",
      textColor: "#374151",
      accentColor: "#e5e7eb",
      fontFamily: "Helvetica",
      showTitle: true
    };
  }

  private generateTemplateDescription(detectionResult: PlaceholderDetectionResult, fieldCount: number): string {
    return `Auto-generated template with ${fieldCount} fields detected using ${detectionResult.detectionMethod} method. Detection confidence: ${Math.round(detectionResult.placeholders.reduce((sum, p) => sum + p.confidence, 0) / detectionResult.placeholders.length * 100)}%`;
  }
}

// Supporting interfaces
interface PlaceholderGroup {
  name: string;
  placeholders: DetectedPlaceholder[];
}

interface FieldGroup {
  name: string;
  fields: FormField[];
  startY: number;
  endY: number;
}
