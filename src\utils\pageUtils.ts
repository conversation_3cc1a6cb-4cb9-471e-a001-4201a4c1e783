export type PageFormat = 'letter' | 'a4' | 'legal';
export type PageOrientation = 'portrait' | 'landscape';

export const PAGE_SIZES: Record<PageFormat, { width: number; height: number }> = {
  letter: { width: 612, height: 792 },
  a4: { width: 595, height: 842 },
  legal: { width: 612, height: 1008 },
};

export const getPageDimensions = (format: PageFormat, orientation: PageOrientation) => {
  const size = PAGE_SIZES[format];
  if (orientation === 'landscape') {
    return { width: size.height, height: size.width };
  }
  return size;
};