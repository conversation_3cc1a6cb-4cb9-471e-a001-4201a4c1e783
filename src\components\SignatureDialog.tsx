import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { SignaturePad } from "./SignaturePad";

interface SignatureDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (signature: string) => void;
}

export const SignatureDialog: React.FC<SignatureDialogProps> = ({
  isOpen,
  onClose,
  onSave,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Draw your signature</DialogTitle>
          <DialogDescription>
            Use your mouse or trackpad to draw your signature below.
          </DialogDescription>
        </DialogHeader>
        <SignaturePad onSave={onSave} />
      </DialogContent>
    </Dialog>
  );
};