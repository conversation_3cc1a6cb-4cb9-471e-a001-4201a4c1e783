import React, { useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface RulerProps {
  orientation: 'horizontal' | 'vertical';
  zoomLevel: number;
  scrollPosition: number;
  mousePosition: number | null;
  onAddGuide: (position: number) => void;
  className?: string;
}

const RULER_SIZE = 20; // px
const TICK_COLOR = '#a0a0a0';
const TEXT_COLOR = '#606060';
const INDICATOR_COLOR = '#f59e0b'; // amber-500

export const Ruler: React.FC<RulerProps> = ({ orientation, zoomLevel, scrollPosition, mousePosition, onAddGuide, className }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const handleClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const isHorizontal = orientation === 'horizontal';
    const pos = isHorizontal ? e.clientX - rect.left : e.clientY - rect.top;
    const value = scrollPosition + pos / zoomLevel;
    onAddGuide(value);
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const isHorizontal = orientation === 'horizontal';
    const { width, height } = canvas.getBoundingClientRect();
    canvas.width = width * window.devicePixelRatio;
    canvas.height = height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    
    ctx.clearRect(0, 0, width, height);
    ctx.fillStyle = '#f0f0f0'; // Ruler background
    ctx.fillRect(0, 0, width, height);

    ctx.strokeStyle = TICK_COLOR;
    ctx.fillStyle = TEXT_COLOR;
    ctx.font = '10px sans-serif';
    ctx.textAlign = isHorizontal ? 'center' : 'left';
    ctx.textBaseline = isHorizontal ? 'top' : 'middle';

    const length = isHorizontal ? width : height;
    const startValue = scrollPosition;
    const endValue = startValue + length / zoomLevel;

    let majorTickSpacing = 100;
    if (zoomLevel > 2) majorTickSpacing = 25;
    else if (zoomLevel > 0.8) majorTickSpacing = 50;
    else if (zoomLevel < 0.4) majorTickSpacing = 200;

    const minorTickSpacing = majorTickSpacing / 5;

    const drawTick = (pos: number, tickHeight: number) => {
      if (isHorizontal) {
        ctx.beginPath();
        ctx.moveTo(pos, RULER_SIZE);
        ctx.lineTo(pos, RULER_SIZE - tickHeight);
        ctx.stroke();
      } else {
        ctx.beginPath();
        ctx.moveTo(RULER_SIZE, pos);
        ctx.lineTo(RULER_SIZE - tickHeight, pos);
        ctx.stroke();
      }
    };

    for (let i = Math.floor(startValue / minorTickSpacing) * minorTickSpacing; i < endValue; i += minorTickSpacing) {
      const screenPos = (i - startValue) * zoomLevel;
      if (i % majorTickSpacing === 0) {
        drawTick(screenPos, 10);
        const label = i.toString();
        if (isHorizontal) {
          ctx.fillText(label, screenPos, 2);
        } else {
          ctx.save();
          ctx.translate(5, screenPos);
          ctx.rotate(-Math.PI / 2);
          ctx.textAlign = 'center';
          ctx.fillText(label, 0, 0);
          ctx.restore();
        }
      } else {
        drawTick(screenPos, 5);
      }
    }

    // Draw mouse position indicator
    if (mousePosition !== null) {
      const mouseScreenPos = (mousePosition - startValue) * zoomLevel;
      ctx.strokeStyle = INDICATOR_COLOR;
      ctx.fillStyle = INDICATOR_COLOR;
      
      if (isHorizontal) {
        ctx.beginPath();
        ctx.moveTo(mouseScreenPos, 0);
        ctx.lineTo(mouseScreenPos, RULER_SIZE);
        ctx.stroke();
        ctx.fillRect(mouseScreenPos - 20, 0, 40, RULER_SIZE);
        ctx.fillStyle = 'white';
        ctx.fillText(Math.round(mousePosition).toString(), mouseScreenPos, 2);
      } else {
        ctx.beginPath();
        ctx.moveTo(0, mouseScreenPos);
        ctx.lineTo(RULER_SIZE, mouseScreenPos);
        ctx.stroke();
        ctx.fillRect(0, mouseScreenPos - 20, RULER_SIZE, 40);
        ctx.fillStyle = 'white';
        ctx.save();
        ctx.translate(5, mouseScreenPos);
        ctx.rotate(-Math.PI / 2);
        ctx.textAlign = 'center';
        ctx.fillText(Math.round(mousePosition).toString(), 0, 0);
        ctx.restore();
      }
    }

  }, [orientation, zoomLevel, scrollPosition, mousePosition, window.devicePixelRatio]);

  return (
    <canvas
      ref={canvasRef}
      className={cn(
        'absolute bg-gray-200 cursor-pointer',
        orientation === 'horizontal' ? 'h-5 w-full top-0 left-0' : 'w-5 h-full top-0 left-0',
        className
      )}
      onClick={handleClick}
    />
  );
};