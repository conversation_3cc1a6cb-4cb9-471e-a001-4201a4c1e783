import React, { useMemo, useState, useRef, useEffect } from "react";
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from "@dnd-kit/core";
import { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { useTemplateContext } from "@/context/TemplateContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { SortableFieldItem } from "./SortableFieldItem";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { PanelSearch } from "./PanelSearch";
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from "@/components/ui/context-menu";
import { ClipboardPaste } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export const FieldsPanel = () => {
  const { 
    template, 
    reorderFields, 
    toggleFieldVisibility, 
    toggleFieldLock,
    duplicateFields,
    deleteFields,
    bringToFront,
    sendToBack,
    groupFields,
    ungroupFields,
    copyFields,
    pasteFields,
    clipboard,
    moveFieldsToPage,
  } = useTemplateContext();
  const { activeFieldIds, setActiveFieldIds, handleFieldSelect } = useEditorUIContext();
  const [searchTerm, setSearchTerm] = useState("");
  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor, { coordinateGetter: sortableKeyboardCoordinates }));
  const itemRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const activeFieldId = activeFieldIds.length === 1 ? activeFieldIds[0] : null;

  useEffect(() => {
    if (activeFieldId && itemRefs.current[activeFieldId]) {
      itemRefs.current[activeFieldId]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
    }
  }, [activeFieldId]);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (over && active.id !== over.id) {
      reorderFields(active.id as string, over.id as string);
    }
  };

  const handleContextMenuSelect = (fieldId: string) => {
    if (!activeFieldIds.includes(fieldId)) {
      handleFieldSelect(fieldId, false);
    }
  };

  const handlePaste = () => {
    const newIds = pasteFields();
    if (newIds.length > 0) {
      setActiveFieldIds(newIds);
    }
  };

  const allFields = useMemo(() => template.pages.flatMap(p => p.fields), [template.pages]);

  const isGroupSelection = useMemo(() => {
    if (activeFieldIds.length < 2) return false;
    const selectedFields = allFields.filter((f) =>
      activeFieldIds.includes(f.id)
    );
    const firstGroupId = selectedFields[0]?.groupId;
    if (!firstGroupId) return false;
    return selectedFields.every((f) => f.groupId === firstGroupId);
  }, [activeFieldIds, allFields]);

  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <Card>
          <CardHeader><CardTitle>Layers</CardTitle><CardDescription>Select and drag to reorder fields. Top of the list is the topmost layer.</CardDescription></CardHeader>
          <PanelSearch
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search layers..."
          />
          <CardContent>
            <Accordion type="multiple" defaultValue={template.pages.map(p => p.id)} className="w-full">
              {template.pages.map((page, pageIndex) => {
                const reversedFields = [...page.fields].reverse();
                const filteredFields = reversedFields.filter(field =>
                  field.label.toLowerCase().includes(searchTerm.toLowerCase())
                );

                if (filteredFields.length === 0 && searchTerm) return null;

                return (
                  <AccordionItem value={page.id} key={page.id}>
                    <AccordionTrigger>Page {pageIndex + 1}</AccordionTrigger>
                    <AccordionContent>
                      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                        <SortableContext items={filteredFields.map((f) => f.id)} strategy={verticalListSortingStrategy}>
                          <div className="space-y-1">
                            {filteredFields.length > 0 ? (
                              filteredFields.map((field, index) => {
                                const isFirstInGroup = field.groupId && field.groupId !== filteredFields[index - 1]?.groupId;
                                const isLastInGroup = field.groupId && field.groupId !== filteredFields[index + 1]?.groupId;

                                return (
                                  <SortableFieldItem
                                    ref={el => { itemRefs.current[field.id] = el; }}
                                    key={field.id}
                                    field={field}
                                    isSelected={activeFieldIds.includes(field.id)}
                                    isGroupSelection={isGroupSelection}
                                    activeFieldIds={activeFieldIds}
                                    onSelect={handleFieldSelect}
                                    onContextMenuSelect={handleContextMenuSelect}
                                    onToggleVisibility={toggleFieldVisibility}
                                    onToggleLock={toggleFieldLock}
                                    onCopy={copyFields}
                                    onDuplicate={(ids) => {
                                      const newIds = duplicateFields(ids);
                                      if (newIds.length > 0) setActiveFieldIds(newIds);
                                    }}
                                    onDelete={deleteFields}
                                    onBringToFront={bringToFront}
                                    onSendToBack={sendToBack}
                                    onGroup={groupFields}
                                    onUngroup={ungroupFields}
                                    isFirstInGroup={isFirstInGroup}
                                    isLastInGroup={isLastInGroup}
                                    onMoveToPage={moveFieldsToPage}
                                    pages={template.pages}
                                    currentPageId={page.id}
                                  />
                                );
                              })
                            ) : (
                              <div className="text-center text-sm text-muted-foreground p-4">No layers on this page.</div>
                            )}
                          </div>
                        </SortableContext>
                      </DndContext>
                    </AccordionContent>
                  </AccordionItem>
                )
              })}
            </Accordion>
          </CardContent>
        </Card>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem onClick={handlePaste} disabled={!clipboard}>
          <ClipboardPaste className="mr-2 h-4 w-4" /> Paste
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
};