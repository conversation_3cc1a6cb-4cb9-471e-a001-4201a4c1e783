import { FormField, TableColumn, TableSummary } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { PlusCircle, Trash2 } from "lucide-react";

interface TablePanelProps {
  field: FormField;
}

export const TablePanel = ({ field }: TablePanelProps) => {
  const { updateField } = useTemplateContext();

  const handleTableColumnChange = (
    index: number,
    newProps: Partial<TableColumn>
  ) => {
    const newColumns = [...(field.columns || [])];
    newColumns[index] = { ...newColumns[index], ...newProps };
    updateField(field.id, { columns: newColumns });
  };

  const addTableColumn = () =>
    updateField(field.id, {
      columns: [
        ...(field.columns || []),
        { id: `new_col_${Date.now()}`, header: "New Column" },
      ],
    });

  const deleteTableColumn = (index: number) =>
    updateField(field.id, {
      columns: (field.columns || []).filter((_, i) => i !== index),
    });

  const handleTableSummaryChange = (
    index: number,
    newProps: Partial<TableSummary>
  ) => {
    const newSummary = [...(field.summary || [])];
    newSummary[index] = { ...newSummary[index], ...newProps };
    updateField(field.id, { summary: newSummary });
  };

  const addTableSummary = () =>
    updateField(field.id, {
      summary: [
        ...(field.summary || []),
        {
          id: `new_summary_${Date.now()}`,
          label: "New Item",
          formula: "SUM(total)",
        },
      ],
    });

  const deleteTableSummary = (index: number) =>
    updateField(field.id, {
      summary: (field.summary || []).filter((_, i) => i !== index),
    });

  return (
    <AccordionItem value="table-config">
      <AccordionTrigger>Table Configuration</AccordionTrigger>
      <AccordionContent className="space-y-6 pt-4">
        <div className="space-y-2">
          <Label>Columns</Label>
          <div className="space-y-2">
            {(field.columns || []).map((col, index) => (
              <div
                key={index}
                className="p-2 border rounded-md space-y-2 bg-muted/50"
              >
                <div className="flex items-center gap-2">
                  <Input
                    placeholder="Header"
                    value={col.header}
                    onChange={(e) =>
                      handleTableColumnChange(index, {
                        header: e.target.value,
                      })
                    }
                  />
                  <Input
                    placeholder="Data ID"
                    value={col.id}
                    onChange={(e) =>
                      handleTableColumnChange(index, {
                        id: e.target.value,
                      })
                    }
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => deleteTableColumn(index)}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
                <Input
                  placeholder="Calculation (e.g., quantity * price)"
                  value={col.calculation || ""}
                  onChange={(e) =>
                    handleTableColumnChange(index, {
                      calculation: e.target.value,
                    })
                  }
                />
              </div>
            ))}
          </div>
          <Button
            variant="outline"
            size="sm"
            className="w-full mt-2"
            onClick={addTableColumn}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Column
          </Button>
        </div>
        <div className="space-y-2">
          <Label>Summary Rows</Label>
          <div className="space-y-2">
            {(field.summary || []).map((item, index) => (
              <div
                key={index}
                className="flex items-center gap-2 p-2 border rounded-md bg-muted/50"
              >
                <Input
                  placeholder="Label"
                  value={item.label}
                  onChange={(e) =>
                    handleTableSummaryChange(index, {
                      label: e.target.value,
                    })
                  }
                />
                <Input
                  placeholder="Formula (e.g., SUM(total))"
                  value={item.formula}
                  onChange={(e) =>
                    handleTableSummaryChange(index, {
                      formula: e.target.value,
                    })
                  }
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => deleteTableSummary(index)}
                >
                  <Trash2 className="h-4 w-4 text-destructive" />
                </Button>
              </div>
            ))}
          </div>
          <Button
            variant="outline"
            size="sm"
            className="w-full mt-2"
            onClick={addTableSummary}
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Summary Row
          </Button>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};