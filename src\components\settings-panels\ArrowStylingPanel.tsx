import { FormField } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ArrowStylingPanelProps {
  field: FormField;
}

export const ArrowStylingPanel = ({ field }: ArrowStylingPanelProps) => {
  const { updateField } = useTemplateContext();

  return (
    <AccordionItem value="styling">
      <AccordionTrigger>Styling</AccordionTrigger>
      <AccordionContent className="space-y-4 pt-4">
        <div className="space-y-2">
          <Label htmlFor="color">Line Color</Label>
          <Input
            id="color"
            type="color"
            value={field.color || "#000000"}
            onChange={(e) => updateField(field.id, { color: e.target.value })}
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="arrowheadSize">Arrowhead Size</Label>
            <Input
              id="arrowheadSize"
              type="number"
              value={field.arrowheadSize || 8}
              onChange={(e) =>
                updateField(field.id, {
                  arrowheadSize: parseInt(e.target.value) || 0,
                })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="arrowDirection">Direction</Label>
            <Select
              value={field.arrowDirection || "right"}
              onValueChange={(v) =>
                updateField(field.id, { arrowDirection: v as any })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="right">Right</SelectItem>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="up">Up</SelectItem>
                <SelectItem value="down">Down</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};