import React from "react";
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from "@dnd-kit/core";
import { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { useTemplateContext } from "@/context/TemplateContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { SortablePageThumbnail } from "./SortablePageThumbnail";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { FilePlus } from "lucide-react";

export const PagesPanel = () => {
  const { template, reorderPages, addPage, deletePage, duplicatePage } = useTemplateContext();
  const { activePageId, setActivePageId } = useEditorUIContext();
  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor, { coordinateGetter: sortableKeyboardCoordinates }));

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (over && active.id !== over.id) {
      reorderPages(active.id as string, over.id as string);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pages</CardTitle>
        <CardDescription>Manage and reorder your document pages.</CardDescription>
      </CardHeader>
      <CardContent>
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <SortableContext items={template.pages.map(p => p.id)} strategy={verticalListSortingStrategy}>
            <div className="space-y-2">
              {template.pages.map((page, index) => (
                <SortablePageThumbnail
                  key={page.id}
                  page={page}
                  template={template}
                  pageIndex={index}
                  isActive={activePageId === page.id}
                  onClick={() => setActivePageId(page.id)}
                  onDuplicate={duplicatePage}
                  onDelete={deletePage}
                  isDeleteDisabled={template.pages.length <= 1}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
        <Button variant="outline" className="w-full mt-4" onClick={addPage}>
          <FilePlus className="mr-2 h-4 w-4" />
          Add New Page
        </Button>
      </CardContent>
    </Card>
  );
};