import React, { useRef, useEffect, useState, useMemo } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Download, Trash2, Edit, PlusCircle, Sparkles, Undo2, Redo2, Save, Upload } from "lucide-react";
import { useTemplateContext } from "@/context/TemplateContext";
import { useFormContext } from "@/context/FormContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { cn } from "@/lib/utils";
import { DatePicker } from "./DatePicker";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { isFieldVisible } from "@/utils/visibility";
import { showError, showSuccess } from "@/utils/toast";
import { Checkbox } from "./ui/checkbox";
import { RadioGroup, RadioGroupItem } from "./ui/radio-group";
import { generatePdfDocument } from "@/utils/pdfGenerator";
import { PanelSearch } from "./PanelSearch";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { FormField } from "@/data/templates";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

export const EditorForm = () => {
  const { template, logo } = useTemplateContext();
  const { 
    formState, 
    handleInputChange, 
    handleClearForm, 
    addTableRow, 
    deleteTableRow, 
    handleTableRowChange, 
    validationErrors, 
    runFullValidation,
    handleFillWithSampleData,
    loadFormState,
    undo,
    redo,
    canUndo,
    canRedo,
  } = useFormContext();
  const { 
    activeFieldIds, 
    handleFieldSelect, 
    openSignatureDialog 
  } = useEditorUIContext();
  const [searchTerm, setSearchTerm] = useState("");
  const [openSections, setOpenSections] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const inputRefs = useRef<Record<string, HTMLElement>>({});
  const activeFieldId = activeFieldIds.length === 1 ? activeFieldIds[0] : null;

  const allFields = useMemo(() => template.pages.flatMap(p => p.fields), [template.pages]);

  const visibleFields = useMemo(() => 
    allFields.filter(field => 
      field.type !== 'static-text' && 
      field.type !== 'box' &&
      isFieldVisible(field, formState) && 
      (field.isVisible ?? true)
    ), [allFields, formState]);

  const filteredFields = useMemo(() => 
    visibleFields.filter(field =>
      field.label.toLowerCase().includes(searchTerm.toLowerCase())
    ), [visibleFields, searchTerm]);

  const groupedFields = useMemo(() => {
    const groups: Record<string, { name: string; fields: FormField[] }> = {};
    const ungrouped: FormField[] = [];

    filteredFields.forEach(field => {
        if (field.groupId) {
            if (!groups[field.groupId]) {
                const firstFieldInGroup = allFields.find(f => f.groupId === field.groupId);
                groups[field.groupId] = {
                    name: `Group: ${firstFieldInGroup?.label || field.groupId}`,
                    fields: []
                };
            }
            groups[field.groupId].fields.push(field);
        } else {
            ungrouped.push(field);
        }
    });

    const sections = [];
    if (ungrouped.length > 0) {
        sections.push({ id: 'ungrouped', name: 'General Fields', fields: ungrouped });
    }
    
    Object.entries(groups).forEach(([groupId, groupData]) => {
        sections.push({ id: groupId, ...groupData });
    });

    return sections;
  }, [filteredFields, allFields]);

  useEffect(() => {
    setOpenSections(groupedFields.map(g => g.id));
  }, [searchTerm, groupedFields]);

  useEffect(() => {
    if (activeFieldId) {
      const field = allFields.find(f => f.id === activeFieldId);
      const sectionId = field?.groupId || 'ungrouped';
      
      if (groupedFields.some(g => g.id === sectionId) && !openSections.includes(sectionId)) {
          setOpenSections(prev => [...prev, sectionId]);
      }
      
      if (inputRefs.current[activeFieldId]) {
        const element = inputRefs.current[activeFieldId];
        setTimeout(() => {
          element.focus();
          element.scrollIntoView({ behavior: "smooth", block: "center" });
        }, 100); // Delay to allow accordion to open
      }
    }
  }, [activeFieldId, allFields, groupedFields, openSections]);

  const handleDownload = async () => {
    if (!runFullValidation()) { showError("Please fix form errors before downloading."); return; }
    try { 
      const doc = await generatePdfDocument(template, formState, logo);
      doc.save(`${template.title.replace(/ /g, "_")}.pdf`); 
      showSuccess("PDF downloaded successfully!"); 
    } 
    catch (e) { showError("Failed to download PDF."); console.error(e); }
  };

  const handleSaveData = () => {
    try {
      const dataStr = JSON.stringify(formState, null, 2);
      const blob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.download = `${template.title.replace(/ /g, "_")}_data.json`;
      link.href = url;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      showSuccess("Form data saved!");
    } catch (e) {
      showError("Failed to save form data.");
      console.error(e);
    }
  };

  const handleLoadDataClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result;
        if (typeof text !== 'string') {
          throw new Error("Failed to read file.");
        }
        const data = JSON.parse(text);
        loadFormState(data);
      } catch (err) {
        showError("Invalid data file. Please upload a valid JSON file.");
        console.error(err);
      }
    };
    reader.readAsText(file);
    event.target.value = '';
  };

  const renderField = (field: FormField) => {
    const error = validationErrors[field.id];
    const fieldWithErrorHandling = (comp: React.ReactNode) => (<>{comp}{error && <p className="text-sm text-destructive mt-1">{error}</p>}</>);
    return (
      <div key={field.id} className={cn("space-y-2 rounded-md p-2 transition-colors", activeFieldId === field.id && "bg-blue-50 dark:bg-blue-900/20")}>
        <Label htmlFor={field.id}>{field.label}</Label>
        {(() => {
          switch (field.type) {
            case "textarea": return fieldWithErrorHandling(<Textarea id={field.id} ref={el => { if (el) inputRefs.current[field.id] = el; }} onFocus={() => handleFieldSelect(field.id, false)} placeholder={field.placeholder} value={formState[field.id] || ""} onChange={e => handleInputChange(field.id, e.target.value)} className={cn(error && "border-destructive focus-visible:ring-destructive")} />);
            case "signature": return fieldWithErrorHandling(<div id={field.id} ref={el => { if (el) inputRefs.current[field.id] = el; }} tabIndex={0} onFocus={() => handleFieldSelect(field.id, false)} className={cn("w-full border-2 border-dashed rounded-md p-4 text-center cursor-pointer hover:border-primary", error && "border-destructive")} onClick={() => openSignatureDialog(field.id)}>{formState[field.id] ? <div className="flex flex-col items-center gap-2"><img src={formState[field.id]} alt="Signature" className="h-12" /><Button variant="link" size="sm" className="text-xs"><Edit className="mr-2 h-3 w-3" /> Change Signature</Button></div> : <span className="text-muted-foreground">Click to sign</span>}</div>);
            case "image":
              const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
                  const file = e.target.files?.[0];
                  if (file) {
                      if (file.size > 2 * 1024 * 1024) { showError("Image must be less than 2MB."); return; }
                      const reader = new FileReader();
                      reader.onloadend = () => { handleInputChange(field.id, reader.result as string); };
                      reader.readAsDataURL(file);
                  }
              };
              return fieldWithErrorHandling(
                  <div id={field.id} ref={el => { if (el) inputRefs.current[field.id] = el; }} tabIndex={0} onFocus={() => handleFieldSelect(field.id, false)} className={cn("w-full border-2 border-dashed rounded-md p-4 text-center hover:border-primary", error && "border-destructive")}>
                      <Input id={`upload-${field.id}`} type="file" className="sr-only" accept="image/png, image/jpeg, image/gif" onChange={handleImageUpload} />
                      <Label htmlFor={`upload-${field.id}`} className="cursor-pointer">
                          {formState[field.id] ? (
                              <div className="flex flex-col items-center gap-2">
                                  <img src={formState[field.id]} alt="Preview" className="max-h-24 object-contain" />
                                  <Button variant="link" size="sm" className="text-xs"><Edit className="mr-2 h-3 w-3" /> Change Image</Button>
                              </div>
                          ) : ( <span className="text-muted-foreground">Click to upload image</span> )}
                      </Label>
                  </div>
              );
            case "date": return fieldWithErrorHandling(<div id={field.id} ref={el => { if (el) inputRefs.current[field.id] = el; }} tabIndex={0} onFocus={() => handleFieldSelect(field.id, false)} className={cn(error && "[&>button]:border-destructive [&>button]:focus-visible:ring-destructive")}><DatePicker value={formState[field.id] || ""} onChange={value => handleInputChange(field.id, value)} /></div>);
            case "select": return fieldWithErrorHandling(<div id={field.id} ref={el => { if (el) inputRefs.current[field.id] = el; }} tabIndex={0} onFocus={() => handleFieldSelect(field.id, false)} className={cn(error && "[&>button]:border-destructive [&>button]:focus-visible:ring-destructive")}><Select value={formState[field.id] || ""} onValueChange={value => handleInputChange(field.id, value)}><SelectTrigger><SelectValue placeholder={field.placeholder} /></SelectTrigger><SelectContent>{field.options?.map(o => <SelectItem key={o} value={o}>{o}</SelectItem>)}</SelectContent></Select></div>);
            case "radio": return fieldWithErrorHandling(<div id={field.id} ref={el => { if (el) inputRefs.current[field.id] = el; }} tabIndex={0} onFocus={() => handleFieldSelect(field.id, false)}><RadioGroup value={formState[field.id] || ""} onValueChange={value => handleInputChange(field.id, value)} className="space-y-2 pt-2">{(field.options || []).map(option => (<div key={option} className="flex items-center space-x-2"><RadioGroupItem value={option} id={`${field.id}-${option}`} /><Label htmlFor={`${field.id}-${option}`}>{option}</Label></div>))}</RadioGroup></div>);
            case "checkbox": return fieldWithErrorHandling(<div className="flex items-center space-x-2 pt-2" id={field.id} ref={el => { if (el) inputRefs.current[field.id] = el; }} tabIndex={0} onFocus={() => handleFieldSelect(field.id, false)}><Checkbox id={`form-${field.id}`} checked={!!formState[field.id]} onCheckedChange={checked => handleInputChange(field.id, !!checked)} /><label htmlFor={`form-${field.id}`} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">{field.placeholder || "Toggle"}</label></div>);
            case "table": return fieldWithErrorHandling(<div className="space-y-2" id={field.id} onFocus={() => handleFieldSelect(field.id, false)}>{(formState[field.id] || []).map((row: any, rowIndex: number) => (<div key={rowIndex} className="grid grid-cols-12 gap-2 items-center p-2 border rounded-md"><div className="col-span-11 grid gap-2" style={{ gridTemplateColumns: `repeat(${field.columns?.length || 1}, minmax(0, 1fr))` }}>{field.columns?.map(col => <Input key={col.id} placeholder={col.header} value={row[col.id] || ""} onChange={e => handleTableRowChange(field.id, rowIndex, col.id, e.target.value)} readOnly={!!col.calculation} className={cn(!!col.calculation && "bg-muted/50")} />)}</div><Button variant="ghost" size="icon" onClick={() => deleteTableRow(field.id, rowIndex)}><Trash2 className="h-4 w-4 text-destructive" /></Button></div>))}<Button variant="outline" className="w-full" onClick={() => addTableRow(field.id)}><PlusCircle className="mr-2 h-4 w-4" /> Add Row</Button></div>);
            default: return fieldWithErrorHandling(<Input id={field.id} ref={el => { if (el) inputRefs.current[field.id] = el; }} onFocus={() => handleFieldSelect(field.id, false)} type={field.type} placeholder={field.placeholder} value={formState[field.id] || ""} onChange={e => handleInputChange(field.id, e.target.value)} className={cn(error && "border-destructive focus-visible:ring-destructive")} />);
          }
        })()}
      </div>
    )
  };

  return (
    <Card>
      <CardHeader><CardTitle>Fill Details</CardTitle><CardDescription>Enter information to populate the PDF. Your progress is saved automatically.</CardDescription></CardHeader>
      <PanelSearch
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder="Search fields..."
      />
      <CardContent className="space-y-1 max-h-[calc(100vh-350px)] overflow-y-auto p-2">
        {groupedFields.length > 0 ? (
          <Accordion type="multiple" value={openSections} onValueChange={setOpenSections} className="w-full">
            {groupedFields.map(({ id, name, fields }) => (
              <AccordionItem value={id} key={id}>
                <AccordionTrigger>{name}</AccordionTrigger>
                <AccordionContent className="space-y-1 p-2">
                  {fields.map(renderField)}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <div className="text-center text-sm text-muted-foreground p-4">No fields found.</div>
        )}
      </CardContent>
      <CardFooter className="flex flex-col gap-2">
        <div className="flex w-full items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button className="w-full" onClick={handleDownload}><Download className="mr-2 h-4 w-4" />Download PDF</Button>
            </TooltipTrigger>
            <TooltipContent><p>Download the filled PDF</p></TooltipContent>
          </Tooltip>
          <Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={undo} disabled={!canUndo}><Undo2 className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent><p>Undo Form Change</p></TooltipContent></Tooltip>
          <Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={redo} disabled={!canRedo}><Redo2 className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent><p>Redo Form Change</p></TooltipContent></Tooltip>
        </div>
        <div className="grid grid-cols-2 gap-2 w-full">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" className="w-full" onClick={handleFillWithSampleData}><Sparkles className="mr-2 h-4 w-4" />Sample Data</Button>
            </TooltipTrigger>
            <TooltipContent><p>Fill the form with random data</p></TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="destructive" className="w-full" onClick={handleClearForm}><Trash2 className="mr-2 h-4 w-4" />Clear Form</Button>
            </TooltipTrigger>
            <TooltipContent><p>Clear all entries from the form</p></TooltipContent>
          </Tooltip>
        </div>
        <div className="grid grid-cols-2 gap-2 w-full">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" className="w-full" onClick={handleSaveData}>
                <Save className="mr-2 h-4 w-4" />
                Save Data
              </Button>
            </TooltipTrigger>
            <TooltipContent><p>Save form data to a JSON file</p></TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" className="w-full" onClick={handleLoadDataClick}>
                <Upload className="mr-2 h-4 w-4" />
                Load Data
              </Button>
            </TooltipTrigger>
            <TooltipContent><p>Load form data from a JSON file</p></TooltipContent>
          </Tooltip>
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept=".json,application/json"
            onChange={handleFileChange}
          />
        </div>
      </CardFooter>
    </Card>
  );
};