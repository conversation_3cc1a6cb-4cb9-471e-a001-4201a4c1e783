import { describe, it, expect } from 'vitest';
import { 
  sanitizeInput, 
  validateTemplateStructure, 
  validateField,
  validateEmail,
  validateFileType,
  validateFileSize
} from '../validation';
import { FormField } from '@/data/templates';

describe('validation utilities', () => {
  describe('sanitizeInput', () => {
    it('should sanitize HTML tags', () => {
      const input = '<script>alert("xss")</script>';
      const result = sanitizeInput(input);
      expect(result).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;');
    });

    it('should handle empty strings', () => {
      expect(sanitizeInput('')).toBe('');
    });

    it('should handle non-string input', () => {
      expect(sanitizeInput(null as any)).toBe('');
      expect(sanitizeInput(undefined as any)).toBe('');
    });
  });

  describe('validateTemplateStructure', () => {
    it('should validate correct template structure', () => {
      const template = {
        title: 'Test Template',
        description: 'A test template',
        pages: [
          {
            id: 'page1',
            fields: [
              {
                id: 'field1',
                type: 'text',
                x: 10,
                y: 20,
                width: 100,
                height: 30
              }
            ]
          }
        ]
      };
      expect(validateTemplateStructure(template)).toBe(true);
    });

    it('should reject templates with missing required fields', () => {
      const template = { title: 'Test' }; // missing pages
      expect(validateTemplateStructure(template)).toBe(false);
    });

    it('should reject templates with malicious content', () => {
      const template = {
        title: 'Test Template',
        pages: [
          {
            id: 'page1',
            fields: [
              {
                id: 'field1',
                type: 'text',
                x: 10,
                y: 20,
                defaultValue: '<script>alert("xss")</script>'
              }
            ]
          }
        ]
      };
      expect(validateTemplateStructure(template)).toBe(false);
    });
  });

  describe('validateField', () => {
    it('should validate required fields', () => {
      const field: FormField = {
        id: 'test',
        label: 'Test Field',
        type: 'text',
        x: 0,
        y: 0,
        width: 100,
        height: 30,
        validation: { required: true }
      };

      expect(validateField(field, '')).toBe('Test Field is required.');
      expect(validateField(field, 'value')).toBe(null);
    });

    it('should validate field length', () => {
      const field: FormField = {
        id: 'test',
        label: 'Test Field',
        type: 'text',
        x: 0,
        y: 0,
        width: 100,
        height: 30,
        validation: { minLength: 5, maxLength: 10 }
      };

      expect(validateField(field, 'abc')).toBe('Must be at least 5 characters.');
      expect(validateField(field, 'abcdefghijk')).toBe('Cannot exceed 10 characters.');
      expect(validateField(field, 'abcdef')).toBe(null);
    });
  });

  describe('validateEmail', () => {
    it('should validate email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('')).toBe(false);
    });
  });

  describe('validateFileType', () => {
    it('should validate JSON files', () => {
      const jsonFile = new File(['{}'], 'test.json', { type: 'application/json' });
      const txtFile = new File(['text'], 'test.txt', { type: 'text/plain' });
      
      expect(validateFileType(jsonFile)).toBe(true);
      expect(validateFileType(txtFile)).toBe(false);
    });
  });

  describe('validateFileSize', () => {
    it('should validate file size limits', () => {
      const smallFile = new File(['small'], 'small.json', { type: 'application/json' });
      const largeContent = 'x'.repeat(6 * 1024 * 1024); // 6MB
      const largeFile = new File([largeContent], 'large.json', { type: 'application/json' });
      
      expect(validateFileSize(smallFile)).toBe(true);
      expect(validateFileSize(largeFile)).toBe(false);
    });
  });
});
