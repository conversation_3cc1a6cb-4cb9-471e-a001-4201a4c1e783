import { FormField } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface BoxStylingPanelProps {
  field: FormField;
}

export const BoxStylingPanel = ({ field }: BoxStylingPanelProps) => {
  const { updateField } = useTemplateContext();

  return (
    <AccordionItem value="styling">
      <AccordionTrigger>Styling</AccordionTrigger>
      <AccordionContent className="space-y-4 pt-4">
        <div className="space-y-2">
          <Label htmlFor="color">Fill Color</Label>
          <Input
            id="color"
            type="color"
            value={field.color || "#000000"}
            onChange={(e) =>
              updateField(field.id, { color: e.target.value })
            }
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="borderColor">Border Color</Label>
          <Input
            id="borderColor"
            type="color"
            value={field.borderColor || "#000000"}
            onChange={(e) =>
              updateField(field.id, { borderColor: e.target.value })
            }
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="borderWidth">Border Width</Label>
            <Input
              id="borderWidth"
              type="number"
              value={field.borderWidth || 0}
              onChange={(e) =>
                updateField(field.id, { borderWidth: parseInt(e.target.value) || 0 })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="borderRadius">Border Radius</Label>
            <Input
              id="borderRadius"
              type="number"
              value={field.borderRadius || 0}
              onChange={(e) =>
                updateField(field.id, { borderRadius: parseInt(e.target.value) || 0 })
              }
            />
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};