import { FormField } from "@/data/templates";

/**
 * Sanitizes user input to prevent XSS attacks
 */
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return '';

  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .replace(/&/g, '&amp;');
};

/**
 * Validates template data structure to prevent malicious content
 */
export const validateTemplateStructure = (template: any): boolean => {
  if (!template || typeof template !== 'object') return false;

  // Required fields
  if (!template.title || !template.pages || !Array.isArray(template.pages)) {
    return false;
  }

  // Sanitize title and description
  if (typeof template.title !== 'string' || template.title.length > 100) {
    return false;
  }

  if (template.description && (typeof template.description !== 'string' || template.description.length > 500)) {
    return false;
  }

  // Validate pages structure
  for (const page of template.pages) {
    if (!page.id || !page.fields || !Array.isArray(page.fields)) {
      return false;
    }

    // Validate fields
    for (const field of page.fields) {
      if (!field.id || !field.type || typeof field.x !== 'number' || typeof field.y !== 'number') {
        return false;
      }

      // Prevent script injection in field properties
      if (field.defaultValue && typeof field.defaultValue === 'string') {
        if (field.defaultValue.includes('<script') || field.defaultValue.includes('javascript:')) {
          return false;
        }
      }
    }
  }

  return true;
};

export const validateField = (
  field: FormField,
  value: any
): string | null => {
  if (!field.validation) {
    return null;
  }

  const { required, minLength, maxLength, minValue, maxValue } = field.validation;

  // Check for required
  if (required && (value === undefined || value === null || value === "")) {
    return `${field.label} is required.`;
  }

  // Skip other validations if value is not present and not required
  if (value === undefined || value === null || value === "") {
    return null;
  }

  const stringValue = String(value);
  const numberValue = parseFloat(stringValue);

  // Check for minLength
  if (minLength !== undefined && stringValue.length < minLength) {
    return `Must be at least ${minLength} characters.`;
  }

  // Check for maxLength
  if (maxLength !== undefined && stringValue.length > maxLength) {
    return `Cannot exceed ${maxLength} characters.`;
  }

  // Check for minValue
  if (minValue !== undefined && (isNaN(numberValue) || numberValue < minValue)) {
    return `Must be at least ${minValue}.`;
  }

  // Check for maxValue
  if (maxValue !== undefined && (isNaN(numberValue) || numberValue > maxValue)) {
    return `Cannot exceed ${maxValue}.`;
  }

  return null;
};

/**
 * Validates all fields in a form and returns errors
 */
export const validateAllFields = (
  fields: FormField[],
  formState: Record<string, any>
): Record<string, string> => {
  const errors: Record<string, string> = {};

  fields.forEach((field) => {
    const error = validateField(field, formState[field.id]);
    if (error) {
      errors[field.id] = error;
    }
  });

  return errors;
};

/**
 * Validates email format
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates file type for template imports
 */
export const validateFileType = (file: File): boolean => {
  return file.type === 'application/json' || file.name.endsWith('.json');
};

/**
 * Validates file size (max 5MB)
 */
export const validateFileSize = (file: File): boolean => {
  const maxSize = 5 * 1024 * 1024; // 5MB
  return file.size <= maxSize;
};