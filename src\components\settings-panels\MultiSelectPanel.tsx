import { useTemplateContext } from "@/context/TemplateContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlignHorizontalSpaceAround,
  AlignVerticalSpaceAround,
  AlignEndVertical,
  AlignCenterHorizontal,
  AlignCenterVertical,
  AlignLeft,
  AlignRight,
  AlignStartVertical,
  Group,
  Ungroup,
  Eye,
  EyeOff,
  Lock,
  Unlock,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { FormField } from "@/data/templates";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useMemo, useState } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export const MultiSelectPanel = () => {
  const {
    template,
    updateMultipleFields,
    alignFields,
    distributeFields,
    groupFields,
    ungroupFields,
    setFieldsVisibility,
    setFieldsLock,
    createGrid,
  } = useTemplateContext();
  const { activeFieldIds, setActiveFieldIds } = useEditorUIContext();
  const [gridConfig, setGridConfig] = useState({ rows: 2, cols: 2, vSpacing: 10, hSpacing: 10 });

  const selectedFields = template.pages
    .flatMap((p) => p.fields)
    .filter((f) => activeFieldIds.includes(f.id));

  const getCommonValue = <K extends keyof FormField>(
    key: K
  ): FormField[K] | "mixed" | undefined => {
    if (selectedFields.length === 0) return undefined;
    const firstValue = selectedFields[0]?.[key];
    if (
      selectedFields.every(
        (f) => JSON.stringify(f[key]) === JSON.stringify(firstValue)
      )
    ) {
      return firstValue;
    }
    return "mixed";
  };

  const isGroup = useMemo(() => {
    if (selectedFields.length < 1) return false;
    const firstGroupId = selectedFields[0].groupId;
    if (!firstGroupId) return false;
    return selectedFields.every((f) => f.groupId === firstGroupId);
  }, [selectedFields]);

  const areAllStylableText = useMemo(() => {
    if (selectedFields.length === 0) return false;
    return selectedFields.every((f) => f.type === "static-text");
  }, [selectedFields]);

  const areAllBoxStylable = useMemo(() => {
    if (selectedFields.length === 0) return false;
    return selectedFields.every((f) => f.type === "box");
  }, [selectedFields]);

  const commonWidth = getCommonValue("width");
  const commonHeight = getCommonValue("height");
  const commonFontSize = getCommonValue("fontSize");
  const commonColor = getCommonValue("color");
  const commonFontWeight = getCommonValue("fontWeight");
  const commonFontStyle = getCommonValue("fontStyle");
  const commonTextAlign = getCommonValue("textAlign");
  const commonBorderColor = getCommonValue("borderColor");
  const commonBorderWidth = getCommonValue("borderWidth");
  const commonBorderRadius = getCommonValue("borderRadius");

  const handleMultiUpdate = (props: Partial<FormField>) => {
    updateMultipleFields(activeFieldIds, props);
  };

  const handleCreateGrid = () => {
    const newIds = createGrid(activeFieldIds, gridConfig);
    setActiveFieldIds([...activeFieldIds, ...newIds]);
  };

  return (
    <div className="p-4 space-y-4">
      <h3 className="font-semibold text-lg">
        {activeFieldIds.length} Fields Selected
      </h3>
      <p className="text-sm text-muted-foreground">
        Edit common properties for all selected fields.
      </p>
      <Accordion
        type="multiple"
        defaultValue={["layout", "arrange", "actions", "styling", "grid"]}
      >
        <AccordionItem value="layout">
          <AccordionTrigger>Dimensions</AccordionTrigger>
          <AccordionContent className="space-y-4 pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="multi-width">Width</Label>
                <Input
                  id="multi-width"
                  type="number"
                  value={commonWidth !== "mixed" ? String(commonWidth ?? "") : ""}
                  placeholder={commonWidth === "mixed" ? "Mixed" : ""}
                  onChange={(e) =>
                    handleMultiUpdate({ width: parseInt(e.target.value) || 0 })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="multi-height">Height</Label>
                <Input
                  id="multi-height"
                  type="number"
                  value={commonHeight !== "mixed" ? String(commonHeight ?? "") : ""}
                  placeholder={commonHeight === "mixed" ? "Mixed" : ""}
                  onChange={(e) =>
                    handleMultiUpdate({
                      height: parseInt(e.target.value) || 0,
                    })
                  }
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {(areAllStylableText || areAllBoxStylable) && (
          <AccordionItem value="styling">
            <AccordionTrigger>Styling</AccordionTrigger>
            <AccordionContent className="space-y-4 pt-4">
              {areAllStylableText && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="multi-fontSize">Font Size</Label>
                      <Input
                        id="multi-fontSize"
                        type="number"
                        value={
                          commonFontSize !== "mixed" ? String(commonFontSize ?? "") : ""
                        }
                        placeholder={
                          commonFontSize === "mixed" ? "Mixed" : "12"
                        }
                        onChange={(e) =>
                          handleMultiUpdate({
                            fontSize: parseInt(e.target.value, 10),
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="multi-color">Color</Label>
                      <Input
                        id="multi-color"
                        type="color"
                        value={
                          commonColor !== "mixed" ? (commonColor as string) : "#000000"
                        }
                        onChange={(e) =>
                          handleMultiUpdate({ color: e.target.value })
                        }
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <div className="space-y-2">
                      <Label htmlFor="multi-fontWeight">Weight</Label>
                      <Select
                        value={
                          commonFontWeight !== "mixed"
                            ? (commonFontWeight as string)
                            : ""
                        }
                        onValueChange={(v) =>
                          handleMultiUpdate({ fontWeight: v as any })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Mixed" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="bold">Bold</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="multi-fontStyle">Style</Label>
                      <Select
                        value={
                          commonFontStyle !== "mixed"
                            ? (commonFontStyle as string)
                            : ""
                        }
                        onValueChange={(v) =>
                          handleMultiUpdate({ fontStyle: v as any })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Mixed" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="italic">Italic</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="multi-textAlign">Align</Label>
                      <Select
                        value={
                          commonTextAlign !== "mixed"
                            ? (commonTextAlign as string)
                            : ""
                        }
                        onValueChange={(v) =>
                          handleMultiUpdate({ textAlign: v as any })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Mixed" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="left">Left</SelectItem>
                          <SelectItem value="center">Center</SelectItem>
                          <SelectItem value="right">Right</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </>
              )}
              {areAllBoxStylable && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="multi-box-color">Fill Color</Label>
                    <Input
                      id="multi-box-color"
                      type="color"
                      value={
                        commonColor !== "mixed" ? (commonColor as string) : "#000000"
                      }
                      onChange={(e) =>
                        handleMultiUpdate({ color: e.target.value })
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="multi-borderColor">Border Color</Label>
                    <Input
                      id="multi-borderColor"
                      type="color"
                      value={
                        commonBorderColor !== "mixed"
                          ? (commonBorderColor as string)
                          : "#000000"
                      }
                      onChange={(e) =>
                        handleMultiUpdate({ borderColor: e.target.value })
                      }
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="multi-borderWidth">Border Width</Label>
                      <Input
                        id="multi-borderWidth"
                        type="number"
                        value={
                          commonBorderWidth !== "mixed"
                            ? String(commonBorderWidth ?? "")
                            : ""
                        }
                        placeholder={
                          commonBorderWidth === "mixed" ? "Mixed" : "0"
                        }
                        onChange={(e) =>
                          handleMultiUpdate({
                            borderWidth: parseInt(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="multi-borderRadius">Border Radius</Label>
                      <Input
                        id="multi-borderRadius"
                        type="number"
                        value={
                          commonBorderRadius !== "mixed"
                            ? String(commonBorderRadius ?? "")
                            : ""
                        }
                        placeholder={
                          commonBorderRadius === "mixed" ? "Mixed" : "0"
                        }
                        onChange={(e) =>
                          handleMultiUpdate({
                            borderRadius: parseInt(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                  </div>
                </>
              )}
            </AccordionContent>
          </AccordionItem>
        )}

        <AccordionItem value="arrange">
          <AccordionTrigger>Arrange & Align</AccordionTrigger>
          <AccordionContent className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label>Align</Label>
              <div className="grid grid-cols-6 gap-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => alignFields("left", activeFieldIds)}
                    >
                      <AlignLeft className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Align Left (Cmd/Ctrl+Alt+L)</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => alignFields("h-center", activeFieldIds)}
                    >
                      <AlignCenterHorizontal className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    Align Horizontal Center (Cmd/Ctrl+Alt+H)
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => alignFields("right", activeFieldIds)}
                    >
                      <AlignRight className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Align Right (Cmd/Ctrl+Alt+R)</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => alignFields("top", activeFieldIds)}
                    >
                      <AlignStartVertical className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Align Top (Cmd/Ctrl+Alt+T)</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => alignFields("v-center", activeFieldIds)}
                    >
                      <AlignCenterVertical className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    Align Vertical Center (Cmd/Ctrl+Alt+V)
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => alignFields("bottom", activeFieldIds)}
                    >
                      <AlignEndVertical className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Align Bottom (Cmd/Ctrl+Alt+B)</TooltipContent>
                </Tooltip>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Distribute</Label>
              <div className="grid grid-cols-2 gap-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() =>
                        distributeFields("horizontal", activeFieldIds)
                      }
                      disabled={activeFieldIds.length < 3}
                    >
                      <AlignHorizontalSpaceAround className="mr-2 h-4 w-4" />
                      Horizontal
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    Distribute Horizontally (Cmd/Ctrl+Alt+Shift+H)
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() =>
                        distributeFields("vertical", activeFieldIds)
                      }
                      disabled={activeFieldIds.length < 3}
                    >
                      <AlignVerticalSpaceAround className="mr-2 h-4 w-4" />
                      Vertical
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    Distribute Vertically (Cmd/Ctrl+Alt+Shift+V)
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
            <Separator />
            <div className="space-y-2">
              {isGroup ? (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => ungroupFields(activeFieldIds)}
                    >
                      <Ungroup className="mr-2 h-4 w-4" />
                      Ungroup
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Ungroup (Cmd/Ctrl+Shift+G)</TooltipContent>
                </Tooltip>
              ) : (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => groupFields(activeFieldIds)}
                      disabled={activeFieldIds.length < 2}
                    >
                      <Group className="mr-2 h-4 w-4" />
                      Group
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Group (Cmd/Ctrl+G)</TooltipContent>
                </Tooltip>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="grid">
          <AccordionTrigger>Create Grid</AccordionTrigger>
          <AccordionContent className="space-y-4 pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="grid-cols">Columns</Label>
                <Input id="grid-cols" type="number" min="1" value={gridConfig.cols} onChange={(e) => setGridConfig(c => ({ ...c, cols: parseInt(e.target.value) || 1 }))} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="grid-rows">Rows</Label>
                <Input id="grid-rows" type="number" min="1" value={gridConfig.rows} onChange={(e) => setGridConfig(c => ({ ...c, rows: parseInt(e.target.value) || 1 }))} />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="grid-hSpacing">Horizontal Spacing</Label>
                <Input id="grid-hSpacing" type="number" value={gridConfig.hSpacing} onChange={(e) => setGridConfig(c => ({ ...c, hSpacing: parseInt(e.target.value) || 0 }))} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="grid-vSpacing">Vertical Spacing</Label>
                <Input id="grid-vSpacing" type="number" value={gridConfig.vSpacing} onChange={(e) => setGridConfig(c => ({ ...c, vSpacing: parseInt(e.target.value) || 0 }))} />
              </div>
            </div>
            <Button className="w-full" onClick={handleCreateGrid}>Create Grid</Button>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="actions">
          <AccordionTrigger>Batch Actions</AccordionTrigger>
          <AccordionContent className="pt-4">
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFieldsVisibility(activeFieldIds, true)}
              >
                <Eye className="h-4 w-4 mr-2" /> Show
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFieldsVisibility(activeFieldIds, false)}
              >
                <EyeOff className="h-4 w-4 mr-2" /> Hide
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFieldsLock(activeFieldIds, true)}
              >
                <Lock className="h-4 w-4 mr-2" /> Lock
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFieldsLock(activeFieldIds, false)}
              >
                <Unlock className="h-4 w-4 mr-2" /> Unlock
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};