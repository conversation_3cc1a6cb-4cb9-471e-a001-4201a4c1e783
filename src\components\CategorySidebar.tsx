import { getUniqueCategories } from "@/data/templates";
import { Button } from "./ui/button";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface CategorySidebarProps {
  selectedCategory: string | null;
  selectedSubcategory: string | null;
  onCategoryChange: (category: string, subcategory: string | null) => void;
}

export const CategorySidebar: React.FC<CategorySidebarProps> = ({
  selectedCategory,
  selectedSubcategory,
  onCategoryChange,
}) => {
  const categoriesData = getUniqueCategories();

  const handleSelect = (
    category: string,
    subcategory: string | null = null
  ) => {
    onCategoryChange(category, subcategory);
  };

  return (
    <div className="space-y-2">
      <h3 className="font-semibold text-lg px-4">Categories</h3>
      <Button
        variant="ghost"
        className={cn(
          "w-full justify-start",
          !selectedCategory ? "bg-accent text-accent-foreground" : ""
        )}
        onClick={() => handleSelect("All Templates")}
      >
        All Templates
      </Button>
      <Accordion
        type="single"
        collapsible
        className="w-full"
        value={selectedCategory || undefined}
        onValueChange={(value) => handleSelect(value)}
      >
        {categoriesData.map(({ category, subcategories }) => (
          <AccordionItem value={category} key={category}>
            <AccordionTrigger
              className={cn(
                "w-full justify-between hover:no-underline px-4 rounded-md",
                selectedCategory === category && !selectedSubcategory
                  ? "bg-accent text-accent-foreground"
                  : "hover:bg-accent/50"
              )}
            >
              {category}
            </AccordionTrigger>
            <AccordionContent className="pt-1">
              {subcategories.map((subcategory) => (
                <Button
                  key={subcategory}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start pl-8",
                    selectedSubcategory === subcategory &&
                      selectedCategory === category
                      ? "bg-accent text-accent-foreground"
                      : ""
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSelect(category, subcategory);
                  }}
                >
                  {subcategory}
                </Button>
              ))}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};