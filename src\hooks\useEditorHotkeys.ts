import { useEffect } from "react";
import { useTemplateContext } from "@/context/TemplateContext";
import { useEditorUIContext } from "@/context/EditorUIContext";

export const useEditorHotkeys = () => {
  const {
    template,
    deleteFields,
    nudgeFields,
    duplicateFields,
    copyFields,
    pasteFields,
    groupFields,
    ungroupFields,
    undo,
    redo,
    alignFields,
    distributeFields,
    bringForward,
    sendBackward,
    bringToFront,
    sendToBack,
  } = useTemplateContext();
  const { activeFieldIds, setActiveFieldIds } = useEditorUIContext();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const target = e.target as HTMLElement;
      if (
        ["INPUT", "TEXTAREA", "SELECT"].includes(target.tagName) ||
        target.isContentEditable
      )
        return;

      const isMac = navigator.platform.toUpperCase().indexOf("MAC") >= 0;
      const isCtrlOrCmd = isMac ? e.metaKey : e.ctrlKey;
      let handled = false;

      if (e.key === "Escape") {
        setActiveFieldIds([]);
        handled = true;
      }

      if (e.key.startsWith("Arrow") && activeFieldIds.length > 0) {
        const nudgeAmount = e.shiftKey ? 10 : 1;
        let dx = 0,
          dy = 0;
        if (e.key === "ArrowUp") dy = -nudgeAmount;
        if (e.key === "ArrowDown") dy = nudgeAmount;
        if (e.key === "ArrowLeft") dx = -nudgeAmount;
        if (e.key === "ArrowRight") dx = nudgeAmount;
        if (dx !== 0 || dy !== 0) {
          nudgeFields(activeFieldIds, dx, dy);
          handled = true;
        }
      }

      if (
        (e.key === "Backspace" || e.key === "Delete") &&
        activeFieldIds.length > 0
      ) {
        deleteFields(activeFieldIds);
        handled = true;
      }

      if (isCtrlOrCmd) {
        switch (e.key.toLowerCase()) {
          case "a":
            setActiveFieldIds(template.pages.flatMap(p => p.fields).map((f) => f.id));
            handled = true;
            break;
          case "c":
            copyFields(activeFieldIds);
            handled = true;
            break;
          case "v":
            const newIdsPaste = pasteFields();
            if (newIdsPaste.length > 0) setActiveFieldIds(newIdsPaste);
            handled = true;
            break;
          case "g":
            e.shiftKey
              ? ungroupFields(activeFieldIds)
              : groupFields(activeFieldIds);
            handled = true;
            break;
          case "z":
            e.shiftKey ? redo() : undo();
            handled = true;
            break;
          case "y":
            redo();
            handled = true;
            break;
          case "d":
            if (activeFieldIds.length > 0) {
              const newIdsDup = duplicateFields(activeFieldIds);
              if (newIdsDup.length > 0) setActiveFieldIds(newIdsDup);
            }
            handled = true;
            break;
          case "]":
            if (activeFieldIds.length > 0) {
              if (e.shiftKey) {
                bringToFront(activeFieldIds[0]);
              } else {
                bringForward(activeFieldIds[0]);
              }
            }
            handled = true;
            break;
          case "[":
            if (activeFieldIds.length > 0) {
              if (e.shiftKey) {
                sendToBack(activeFieldIds[0]);
              } else {
                sendBackward(activeFieldIds[0]);
              }
            }
            handled = true;
            break;
        }
      }

      if (isCtrlOrCmd && e.altKey && activeFieldIds.length > 1) {
        switch (e.key.toLowerCase()) {
          case "l":
            alignFields("left", activeFieldIds);
            handled = true;
            break;
          case "r":
            alignFields("right", activeFieldIds);
            handled = true;
            break;
          case "t":
            alignFields("top", activeFieldIds);
            handled = true;
            break;
          case "b":
            alignFields("bottom", activeFieldIds);
            handled = true;
            break;
          case "h":
            e.shiftKey
              ? distributeFields("horizontal", activeFieldIds)
              : alignFields("h-center", activeFieldIds);
            handled = true;
            break;
          case "v":
            e.shiftKey
              ? distributeFields("vertical", activeFieldIds)
              : alignFields("v-center", activeFieldIds);
            handled = true;
            break;
        }
      }

      if (handled) e.preventDefault();
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    template.pages,
    activeFieldIds,
    setActiveFieldIds,
    deleteFields,
    nudgeFields,
    duplicateFields,
    copyFields,
    pasteFields,
    groupFields,
    ungroupFields,
    undo,
    redo,
    alignFields,
    distributeFields,
    bringForward,
    sendBackward,
    bringToFront,
    sendToBack,
  ]);
};