import { FieldSettingsPanel } from "./FieldSettingsPanel";
import { DesignPanel } from "./DesignPanel";
import { LogoPanel } from "./LogoPanel";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { Card, CardHeader, CardTitle, CardDescription } from "./ui/card";
import { WatermarkPanel } from "./WatermarkPanel";
import { PageBackgroundPanel } from "./PageBackgroundPanel";
import { PageSetupPanel } from "./PageSetupPanel";

export const RightSidebar = () => {
  const { activeFieldIds } = useEditorUIContext();

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-background overflow-y-auto">
      {activeFieldIds.length > 0 ? (
        <FieldSettingsPanel />
      ) : (
        <>
          <Card className="border-none shadow-none rounded-none">
            <CardHeader>
              <CardTitle>Document Settings</CardTitle>
              <CardDescription>
                Customize the overall look and feel of your document.
              </CardDescription>
            </CardHeader>
          </Card>
          <PageSetupPanel />
          <DesignPanel />
          <PageBackgroundPanel />
          <LogoPanel />
          <WatermarkPanel />
        </>
      )}
    </div>
  );
};