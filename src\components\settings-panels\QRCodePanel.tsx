import React from "react";
import { FormField } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface QRCodePanelProps {
  field: FormField;
}

export const QRCodePanel = ({ field }: QRCodePanelProps) => {
  const { template, updateField } = useTemplateContext();
  const textAreaRef = React.useRef<HTMLTextAreaElement>(null);

  const otherFields = template.pages.flatMap(p => p.fields).filter(
    (f) =>
      f.id !== field.id &&
      f.type !== "static-text" &&
      f.type !== "table" &&
      f.type !== "signature"
  );

  const handleInsertPlaceholder = (fieldIdToInsert: string) => {
    const placeholder = `{{${fieldIdToInsert}}}`;
    const textarea = textAreaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;
    const newText = text.substring(0, start) + placeholder + text.substring(end);

    updateField(field.id, { defaultValue: newText });

    setTimeout(() => {
      textarea.focus();
      textarea.selectionStart = textarea.selectionEnd = start + placeholder.length;
    }, 0);
  };

  return (
    <AccordionItem value="qrcode-content">
      <AccordionTrigger>QR Code Content</AccordionTrigger>
      <AccordionContent className="space-y-4 pt-4">
        <div className="space-y-2">
          <Label htmlFor="defaultValue">Content</Label>
          <Textarea
            id="defaultValue"
            ref={textAreaRef}
            value={(field.defaultValue as string) || ""}
            onChange={(e) =>
              updateField(field.id, { defaultValue: e.target.value })
            }
            placeholder="Enter a URL or text for the QR code..."
          />
          <p className="text-xs text-muted-foreground">
            Use the dropdown below to insert dynamic values from other fields.
          </p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="insert-placeholder">Insert Field</Label>
          <Select onValueChange={handleInsertPlaceholder} value="">
            <SelectTrigger id="insert-placeholder">
              <SelectValue placeholder="Select a field to insert..." />
            </SelectTrigger>
            <SelectContent>
              {otherFields.map((f) => (
                <SelectItem key={f.id} value={f.id}>
                  {f.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};