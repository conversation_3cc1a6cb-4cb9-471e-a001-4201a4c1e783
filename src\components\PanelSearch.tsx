import { Search } from "lucide-react";
import { Input } from "./ui/input";

interface PanelSearchProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
}

export const PanelSearch: React.FC<PanelSearchProps> = ({
  value,
  onChange,
  placeholder,
}) => {
  return (
    <div className="relative px-4 pb-2">
      <Search className="absolute left-7 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input
        type="search"
        placeholder={placeholder || "Search..."}
        className="pl-8 h-9"
        value={value}
        onChange={onChange}
      />
    </div>
  );
};