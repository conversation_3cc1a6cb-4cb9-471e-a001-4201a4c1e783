import React, { useState, useRef, useEffect } from "react";
import { Link, useParams } from "react-router-dom";
import { ArrowLeft, FilePlus } from "lucide-react";
import { PdfPreview } from "@/components/PdfPreview";
import { TemplateProvider, useTemplateContext } from "@/context/TemplateContext";
import { FormProvider } from "@/context/FormContext";
import { EditorUIProvider, useEditorUIContext } from "@/context/EditorUIContext";
import { EditorHeader } from "@/components/EditorHeader";
import { ZoomControls } from "@/components/ZoomControls";
import { SendDialog } from "@/components/SendDialog";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable";
import { SaveTemplateDialog } from "@/components/SaveTemplateDialog";
import {
  DndContext,
  DragEndEvent,
  DragO<PERSON>lay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useS<PERSON><PERSON>,
  useDroppable,
} from "@dnd-kit/core";
import { FormField, Template, getAllTemplates } from "@/data/templates";
import { cn } from "@/lib/utils";
import { LeftSidebar } from "@/components/LeftSidebar";
import { RightSidebar } from "@/components/RightSidebar";
import { useCanvasControls } from "@/hooks/useCanvasControls";
import { useEditorHotkeys } from "@/hooks/useEditorHotkeys";
import { Button } from "@/components/ui/button";
import { Ruler } from "@/components/Ruler";

const EditorContent = () => {
  const { template, addPage } = useTemplateContext();
  const { isPreviewMode, zoomLevel, setZoomLevel, activePageId, addGuide } = useEditorUIContext();
  const { isOver, setNodeRef: setDroppableNodeRef } = useDroppable({
    id: "pdf-preview-droppable-area",
  });
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [scrollPos, setScrollPos] = useState({ top: 0, left: 0 });
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);
  const pageRefs = useRef<Record<string, HTMLDivElement | null>>({});
  useCanvasControls(scrollContainerRef, zoomLevel, setZoomLevel);

  useEffect(() => {
    if (activePageId && pageRefs.current[activePageId]) {
      pageRefs.current[activePageId]?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, [activePageId]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
        setScrollPos({ top: container.scrollTop, left: container.scrollLeft });
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => container.removeEventListener('scroll', handleScroll);
  }, []);

  const handleFitToScreen = () => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const PAGE_WIDTH = 612;
    const PAGE_HEIGHT = 792;
    const PADDING = 80;

    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;

    const zoomX = containerWidth / (PAGE_WIDTH + PADDING);
    const zoomY = containerHeight / (PAGE_HEIGHT + PADDING);

    const newZoom = Math.min(zoomX, zoomY);
    setZoomLevel(newZoom);

    setTimeout(() => {
      if (scrollContainerRef.current) {
        const newContainer = scrollContainerRef.current;
        const contentWidth = PAGE_WIDTH * newZoom;
        const contentHeight = PAGE_HEIGHT * newZoom;
        newContainer.scrollLeft = (contentWidth - newContainer.clientWidth) / 2;
        newContainer.scrollTop = (contentHeight - newContainer.clientHeight) / 2;
      }
    }, 0);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const rect = container.getBoundingClientRect();
    const x = (e.clientX - rect.left + container.scrollLeft) / zoomLevel;
    const y = (e.clientY - rect.top + container.scrollTop) / zoomLevel;
    setMousePosition({ x, y });
  };

  const combinedRef = (node: HTMLDivElement | null) => {
    scrollContainerRef.current = node;
    setDroppableNodeRef(node);
  };

  const renderPages = () => (
    <div className="space-y-4">
      {template.pages.map((page, index) => (
        <div
          key={page.id}
          ref={el => pageRefs.current[page.id] = el}
          data-page-id={page.id}
          className="page-wrapper group relative"
        >
          <PdfPreview template={template} page={page} pageIndex={index} />
        </div>
      ))}
    </div>
  );

  if (isPreviewMode) {
    return (
      <div
        ref={scrollContainerRef}
        className="flex-1 bg-gray-100 dark:bg-gray-900/50 p-4 overflow-auto relative"
      >
        <div
          style={{ transform: `scale(${zoomLevel})`, transformOrigin: "top" }}
          className="transition-transform duration-200 w-min mx-auto"
        >
          {renderPages()}
        </div>
        <ZoomControls onFitToScreen={handleFitToScreen} />
      </div>
    );
  }

  return (
    <ResizablePanelGroup direction="horizontal" className="flex-1">
      <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
        <LeftSidebar />
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel defaultSize={60} minSize={40}>
        <div className="relative h-full w-full overflow-hidden bg-gray-100 dark:bg-gray-900/50">
          <div className="absolute top-0 left-0 w-5 h-5 bg-gray-200 z-30 border-r border-b border-gray-300" />
          <Ruler orientation="horizontal" zoomLevel={zoomLevel} scrollPosition={scrollPos.left / zoomLevel} mousePosition={mousePosition ? mousePosition.x : null} onAddGuide={(pos) => addGuide('horizontal', pos)} className="top-0 left-5 z-20" />
          <Ruler orientation="vertical" zoomLevel={zoomLevel} scrollPosition={scrollPos.top / zoomLevel} mousePosition={mousePosition ? mousePosition.y : null} onAddGuide={(pos) => addGuide('vertical', pos)} className="left-0 top-5 z-20" />
          <div
            ref={combinedRef}
            className={cn(
              "absolute top-5 left-5 right-0 bottom-0 overflow-auto transition-colors",
              isOver && "bg-blue-100 dark:bg-blue-900/50"
            )}
            onMouseMove={handleMouseMove}
            onMouseLeave={() => setMousePosition(null)}
          >
            <div
              id="pdf-preview-wrapper"
              style={{ transform: `scale(${zoomLevel})`, transformOrigin: "top center" }}
              className="transition-transform duration-200 w-min mx-auto p-4"
            >
              {renderPages()}
              <div className="flex justify-center py-4">
                <Button onClick={addPage}>
                  <FilePlus className="mr-2 h-4 w-4" />
                  Add Page
                </Button>
              </div>
            </div>
          </div>
          <ZoomControls onFitToScreen={handleFitToScreen} />
        </div>
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
        <RightSidebar />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
};

const EditorLayout = () => {
  const { addField } = useTemplateContext();
  const { handleFieldSelect, zoomLevel } = useEditorUIContext();
  useEditorHotkeys();

  const [activeDragData, setActiveDragData] = useState<{
    type: string;
    label: string;
    icon: React.ElementType;
  } | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    if (event.active.id.toString().startsWith("tool-")) {
      setActiveDragData(
        event.active.data.current as {
          type: string;
          label: string;
          icon: React.ElementType;
        }
      );
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over, delta } = event;
    setActiveDragData(null);
  
    if (
      over?.id === "pdf-preview-droppable-area" &&
      active.id.toString().startsWith("tool-")
    ) {
      const fieldType = active.data.current?.type as FormField["type"];
      const fieldLabel = active.data.current?.label as string;
      if (!fieldType) return;
  
      const scrollContainer = document.querySelector('[data-radix-scroll-area-viewport]');
      const scrollTop = scrollContainer ? scrollContainer.scrollTop : 0;
  
      const dropYAbsolute = active.rect.current.initial!.top + delta.y;
  
      const pageWrappers = Array.from(document.querySelectorAll('.page-wrapper'));
      let targetPageId = null;
      let targetPageTop = 0;
  
      for (const wrapper of pageWrappers) {
        const rect = wrapper.getBoundingClientRect();
        if (dropYAbsolute >= rect.top && dropYAbsolute <= rect.bottom) {
          targetPageId = wrapper.getAttribute('data-page-id');
          targetPageTop = rect.top;
          break;
        }
      }
  
      if (!targetPageId) {
        const lastPage = pageWrappers[pageWrappers.length - 1];
        if (lastPage) {
          targetPageId = lastPage.getAttribute('data-page-id');
          targetPageTop = lastPage.getBoundingClientRect().top;
        }
      }
  
      if (!targetPageId) return;
  
      const previewWrapper = document.getElementById("pdf-preview-wrapper");
      if (!previewWrapper) return;
  
      const previewRect = previewWrapper.getBoundingClientRect();
  
      const dropX = active.rect.current.initial!.left + delta.x - previewRect.left;
      const dropY = dropYAbsolute - targetPageTop;
  
      const finalX = dropX / zoomLevel;
      const finalY = dropY / zoomLevel;
  
      const newFieldId = addField(fieldType, fieldLabel, {
        x: finalX - 75,
        y: finalY - 15,
      }, targetPageId);
  
      if (newFieldId) {
        handleFieldSelect(newFieldId, false);
      }
    }
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="flex flex-col h-screen">
        <EditorHeader />
        <main className="flex-1 flex overflow-hidden">
          <EditorContent />
        </main>
        <SendDialog />
        <SaveTemplateDialog />
      </div>
      <DragOverlay>
        {activeDragData ? (
          <div className="flex flex-col items-center justify-center gap-2 p-2 border rounded-md bg-background shadow-lg text-center w-24 h-20">
            <activeDragData.icon className="h-6 w-6" />
            <span className="text-xs">{activeDragData.label}</span>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};

const EditorPage = () => {
  const { templateTitle } = useParams<{ templateTitle: string }>();
  const decodedTitle = templateTitle ? decodeURIComponent(templateTitle) : "";
  const allTemplates = getAllTemplates();
  const template = allTemplates.find((t) => t.title === decodedTitle);

  if (!template) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <p className="text-2xl mb-4">Template not found!</p>
        <Link to="/" className="text-blue-500 hover:underline">
          <ArrowLeft className="inline-block mr-2" />
          Back to Home
        </Link>
      </div>
    );
  }

  return (
    <TemplateProvider template={template}>
      <FormProvider>
        <EditorUIProvider>
          <EditorLayout />
        </EditorUIProvider>
      </FormProvider>
    </TemplateProvider>
  );
};

export default EditorPage;