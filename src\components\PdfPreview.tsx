import React, { useMemo, useState, useEffect, useRef } from "react";
import { Template, defaultDesignSettings, FormField, Page, defaultPageSetup } from "@/data/templates";
import { cn } from "@/lib/utils";
import { useOptionalTemplateContext } from "@/context/TemplateContext";
import { useOptionalFormContext } from "@/context/FormContext";
import { useOptionalEditorUIContext } from "@/context/EditorUIContext";
import { Rnd, DraggableData } from "react-rnd";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubTrigger,
  ContextMenuSubContent,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import {
  Copy,
  ClipboardPaste,
  Trash2,
  Group,
  Ungroup,
  ChevronsUp,
  ChevronsDown,
  AlignLeft,
  AlignCenterHorizontal,
  AlignRight,
  AlignStartVertical,
  AlignCenterVertical,
  AlignEndVertical,
  AlignHorizontalSpaceAround,
  AlignVerticalSpaceAround,
  ArrowUp,
  ArrowDown,
  BoxSelect,
  Move,
} from "lucide-react";
import { getSnapGuides } from "@/utils/snapGuides";
import { isFieldVisible } from "@/utils/visibility";
import FormFieldComponent from "./FormFieldComponent";
import { GuideLine } from "./GuideLine";
import { getPageDimensions } from "@/utils/pageUtils";

interface PdfPreviewProps {
  variant?: "full" | "card";
  template: Template;
  page: Page;
  pageIndex: number;
  formState?: Record<string, any>;
}

export const PdfPreview: React.FC<PdfPreviewProps> = ({
  variant = "full",
  template,
  page,
  pageIndex,
  formState: formStateProp,
}) => {
  const templateContext = useOptionalTemplateContext();
  const formCtx = useOptionalFormContext();
  const uiCtx = useOptionalEditorUIContext();

  const [activeSnapLines, setActiveSnapLines] = useState<any[]>([]);
  const [marquee, setMarquee] = useState<{ start: { x: number; y: number }; end: { x: number; y: number } } | null>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  const formState = formStateProp || formCtx?.formState || {};
  const logo = templateContext?.logo;
  const activeFieldIds = uiCtx?.activeFieldIds || [];
  const setActiveFieldIds = uiCtx?.setActiveFieldIds;
  const isPreviewMode = uiCtx?.isPreviewMode;
  const handleFieldSelect = uiCtx?.handleFieldSelect;
  const hoveredFieldId = uiCtx?.hoveredFieldId;
  const horizontalGuides = uiCtx?.horizontalGuides || [];
  const verticalGuides = uiCtx?.verticalGuides || [];
  const inlineEditingFieldId = uiCtx?.inlineEditingFieldId;
  const setInlineEditingFieldId = uiCtx?.setInlineEditingFieldId;
  const isShiftPressed = uiCtx?.isShiftPressed ?? false;
  const updateLogo = templateContext?.updateLogo;
  const updateField = templateContext?.updateField;
  const nudgeFields = templateContext?.nudgeFields;
  const bulkUpdateFields = templateContext?.bulkUpdateFields;
  const duplicateFields = templateContext?.duplicateFields;
  const copyFields = templateContext?.copyFields;
  const pasteFields = templateContext?.pasteFields;
  const clipboard = templateContext?.clipboard;
  const deleteFields = templateContext?.deleteFields;
  const bringForward = templateContext?.bringForward;
  const sendBackward = templateContext?.sendBackward;
  const bringToFront = templateContext?.bringToFront;
  const sendToBack = templateContext?.sendToBack;
  const groupFields = templateContext?.groupFields;
  const ungroupFields = templateContext?.ungroupFields;
  const alignFields = templateContext?.alignFields;
  const distributeFields = templateContext?.distributeFields;
  const moveFieldsToPage = templateContext?.moveFieldsToPage;

  const isCard = variant === "card";
  const isInteractive = !!(templateContext && handleFieldSelect && !isPreviewMode && !isCard);

  const pageSetup = template.pageSetup || defaultPageSetup;
  const pageDimensions = getPageDimensions(pageSetup.format, pageSetup.orientation);
  const allFields = template.pages.flatMap(p => p.fields);

  const isGroupSelection = useMemo(() => {
    if (activeFieldIds.length < 2 || !template) return false;
    const selectedFields = allFields.filter((f) =>
      activeFieldIds.includes(f.id)
    );
    const firstGroupId = selectedFields[0]?.groupId;
    if (!firstGroupId) return false;
    return selectedFields.every((f) => f.groupId === firstGroupId);
  }, [activeFieldIds, template, allFields]);

  const isSelectionLocked = useMemo(() => {
    if (activeFieldIds.length === 0) return false;
    const selectedFields = allFields.filter(f => activeFieldIds.includes(f.id));
    return selectedFields.some(f => f.isLocked);
  }, [activeFieldIds, allFields]);

  const handleSelectAll = () => {
    if (template && setActiveFieldIds) {
      setActiveFieldIds(template.pages.flatMap(p => p.fields).map(f => f.id));
    }
  };

  if (!template)
    return <div className="p-4 text-center text-red-500">Template not found.</div>;

  const { primaryColor, textColor, accentColor, fontFamily, showTitle } =
    template.designSettings || defaultDesignSettings;
  const watermark = template.watermark;

  let selectionBounds = null;
  if (isInteractive && activeFieldIds.length > 1) {
    const selectedFields = allFields.filter((f) =>
      activeFieldIds.includes(f.id)
    );
    if (selectedFields.length > 0) {
      const minX = Math.min(...selectedFields.map((f) => f.x));
      const minY = Math.min(...selectedFields.map((f) => f.y));
      const maxX = Math.max(...selectedFields.map((f) => f.x + f.width));
      const maxY = Math.max(...selectedFields.map((f) => f.y + f.height));
      selectionBounds = {
        x: minX,
        y: minY,
        width: maxX - minX,
        height: maxY - minY,
      };
    }
  }

  const visibleFields = page.fields.filter((field) =>
    isFieldVisible(field, formState) && (field.isVisible ?? true)
  );

  const handleDrag = (field: FormField, data: DraggableData) => {
    if (!isInteractive || !template) return;
    const draggedFields = activeFieldIds.includes(field.id)
      ? allFields.filter(f => activeFieldIds.includes(f.id))
      : [field];
    const staticFields = allFields.filter(f => !activeFieldIds.includes(f.id));
    const dx = data.x - field.x;
    const dy = data.y - field.y;
    const currentDraggedFields = draggedFields.map(f => ({...f, x: f.x + dx, y: f.y + dy}));
    const { guides } = getSnapGuides(currentDraggedFields, staticFields, horizontalGuides, verticalGuides, pageDimensions);
    setActiveSnapLines(guides);
  };

  const handleDragStop = (field: FormField, data: DraggableData) => {
    setActiveSnapLines([]);
    if (!isInteractive || !nudgeFields || !updateField || !template) return;
    const draggedFields = activeFieldIds.includes(field.id)
      ? allFields.filter(f => activeFieldIds.includes(f.id))
      : [field];
    const staticFields = allFields.filter(f => !activeFieldIds.includes(f.id));
    const dx = data.x - field.x;
    const dy = data.y - field.y;
    const currentDraggedFields = draggedFields.map(f => ({...f, x: f.x + dx, y: f.y + dy}));
    const { delta } = getSnapGuides(currentDraggedFields, staticFields, horizontalGuides, verticalGuides, pageDimensions);
    const finalDx = dx + delta.x;
    const finalDy = dy + delta.y;
    if (draggedFields.length > 1) {
      nudgeFields(activeFieldIds, Math.round(finalDx), Math.round(finalDy));
    } else {
      updateField(field.id, { x: Math.round(field.x + finalDx), y: Math.round(field.y + finalDy) });
    }
  };

  const handleMouseDownOnCanvas = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target !== previewRef.current || !isInteractive || !previewRef.current) return;
    const rect = previewRef.current.getBoundingClientRect();
    const startPos = { x: e.clientX - rect.left, y: e.clientY - rect.top };
    setMarquee({ start: startPos, end: startPos });
    const handleMouseMove = (moveEvent: MouseEvent) => {
      setMarquee(prev => prev ? { ...prev, end: { x: moveEvent.clientX - rect.left, y: moveEvent.clientY - rect.top } } : null);
    };
    const handleMouseUp = () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      setMarquee(prevMarquee => {
        if (!prevMarquee || !template || !setActiveFieldIds) return null;
        const { start, end } = prevMarquee;
        const zoom = uiCtx?.zoomLevel || 1;
        const marqueeRect = {
          x: Math.min(start.x, end.x) / zoom,
          y: Math.min(start.y, end.y) / zoom,
          width: Math.abs(start.x - end.x) / zoom,
          height: Math.abs(start.y - end.y) / zoom,
        };
        if (marqueeRect.width < 5 && marqueeRect.height < 5) {
          setActiveFieldIds([]);
          return null;
        }
        const selectedIds = visibleFields.filter(field => {
          const fieldRect = { x: field.x, y: field.y, width: field.width, height: field.height };
          return !(fieldRect.x > marqueeRect.x + marqueeRect.width || fieldRect.x + fieldRect.width < marqueeRect.x || fieldRect.y > marqueeRect.y + marqueeRect.height || fieldRect.y + fieldRect.height < marqueeRect.y);
        }).map(f => f.id);
        setActiveFieldIds(selectedIds);
        return null;
      });
    };
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
  };

  const marqueeStyle: React.CSSProperties = useMemo(() => {
    if (!marquee) return { display: 'none' };
    const { start, end } = marquee;
    return {
      position: 'absolute',
      left: Math.min(start.x, end.x),
      top: Math.min(start.y, end.y),
      width: Math.abs(start.x - end.x),
      height: Math.abs(start.y - end.y),
      border: '1px solid #0ea5e9',
      backgroundColor: 'rgba(14, 165, 233, 0.1)',
      zIndex: 30,
      pointerEvents: 'none',
    };
  }, [marquee]);

  return (
    <ContextMenu>
      <ContextMenuTrigger disabled={!isInteractive} asChild>
        <div
          ref={previewRef}
          className={cn("relative bg-white overflow-hidden", isCard ? "" : "shadow-lg border")}
          style={{
            width: pageDimensions.width,
            height: pageDimensions.height,
            color: textColor,
            borderColor: accentColor,
            fontFamily: fontFamily,
          }}
          onMouseDown={handleMouseDownOnCanvas}
        >
          {page.backgroundImage && (
            <img
              src={page.backgroundImage.src}
              className="absolute top-0 left-0 w-full h-full object-cover pointer-events-none z-0"
              style={{ opacity: page.backgroundImage.opacity }}
            />
          )}
          {watermark?.enabled && (
            <div
              className="absolute inset-0 flex items-center justify-center pointer-events-none z-0"
            >
              <div
                className="break-all text-center"
                style={{
                  fontSize: `${watermark.fontSize}px`,
                  color: watermark.color,
                  opacity: watermark.opacity,
                  transform: `rotate(${watermark.rotation}deg)`,
                  lineHeight: 1,
                }}
              >
                {watermark.text}
              </div>
            </div>
          )}
          {isInteractive && horizontalGuides.map((pos, index) => (
            <GuideLine key={`h-guide-${index}`} orientation="horizontal" position={pos} index={index} />
          ))}
          {isInteractive && verticalGuides.map((pos, index) => (
            <GuideLine key={`v-guide-${index}`} orientation="vertical" position={pos} index={index} />
          ))}
          {(showTitle ?? true) && pageIndex === 0 && (
            <div className={cn("text-center pointer-events-none", isCard ? "p-4" : "p-8")}>
              <h1
                className={cn("font-bold", isCard ? "text-lg" : "text-2xl")}
                style={{ color: primaryColor }}
              >
                {template.title}
              </h1>
            </div>
          )}
          <div className="absolute top-0 left-0 w-full h-full">
            {isInteractive && logo && updateLogo && pageIndex === 0 && (
              <Rnd
                size={{ width: logo.width, height: logo.height }}
                position={{ x: logo.x, y: logo.y }}
                onDragStop={(e, d) => updateLogo({ x: Math.round(d.x), y: Math.round(d.y) })}
                onResizeStop={(e, dir, ref, delta, pos) =>
                  updateLogo({
                    width: parseInt(ref.style.width, 10),
                    height: parseInt(ref.style.height, 10),
                    x: Math.round(pos.x),
                    y: Math.round(pos.y),
                  })
                }
                bounds="parent"
                className="border-2 border-dashed border-blue-400 z-20"
              >
                <img src={logo.src} alt="Logo" className="w-full h-full" />
              </Rnd>
            )}
            {!isInteractive && logo && pageIndex === 0 && (
              <div style={{ position: "absolute", left: logo.x, top: logo.y, width: logo.width, height: logo.height }}>
                <img src={logo.src} alt="Logo" className="w-full h-full" />
              </div>
            )}
            {isInteractive && selectionBounds && bulkUpdateFields && nudgeFields && (
              <Rnd
                size={{ width: selectionBounds.width, height: selectionBounds.height }}
                position={{ x: selectionBounds.x, y: selectionBounds.y }}
                disableDragging={isSelectionLocked}
                enableResizing={!isSelectionLocked}
                onDragStop={(e, d) => {
                  const dx = d.x - selectionBounds.x;
                  const dy = d.y - selectionBounds.y;
                  if (Math.abs(dx) > 0 || Math.abs(dy) > 0) {
                    nudgeFields(activeFieldIds, Math.round(dx), Math.round(dy));
                  }
                }}
                onResizeStop={(e, dir, ref, delta, pos) => {
                  const oldBounds = selectionBounds;
                  const newBounds = { x: pos.x, y: pos.y, width: parseFloat(ref.style.width), height: parseFloat(ref.style.height) };
                  if (oldBounds.width === 0 || oldBounds.height === 0) return;
                  const scaleX = newBounds.width / oldBounds.width;
                  const scaleY = newBounds.height / oldBounds.height;
                  const selectedFields = allFields.filter(f => activeFieldIds.includes(f.id));
                  const updates = selectedFields.map(field => {
                    const relX = field.x - oldBounds.x;
                    const relY = field.y - oldBounds.y;
                    return {
                      id: field.id,
                      x: Math.round(newBounds.x + (relX * scaleX)),
                      y: Math.round(newBounds.y + (relY * scaleY)),
                      width: Math.round(field.width * scaleX),
                      height: Math.round(field.height * scaleY),
                    };
                  });
                  bulkUpdateFields(updates);
                }}
                bounds="parent"
                lockAspectRatio={isShiftPressed}
                className={cn("z-20", isGroupSelection ? "border-2 border-solid border-purple-500" : "border border-dashed border-gray-400", isSelectionLocked && "border-red-500")}
                style={{ pointerEvents: 'none' }}
                resizeHandleStyles={{
                  top: { pointerEvents: 'auto', cursor: 'ns-resize' }, bottom: { pointerEvents: 'auto', cursor: 'ns-resize' },
                  left: { pointerEvents: 'auto', cursor: 'ew-resize' }, right: { pointerEvents: 'auto', cursor: 'ew-resize' },
                  topLeft: { pointerEvents: 'auto', cursor: 'nwse-resize' }, topRight: { pointerEvents: 'auto', cursor: 'nesw-resize' },
                  bottomLeft: { pointerEvents: 'auto', cursor: 'nesw-resize' }, bottomRight: { pointerEvents: 'auto', cursor: 'nwse-resize' },
                }}
                dragHandleClassName="drag-handle-for-selection"
              >
                <div className="drag-handle-for-selection w-full h-full" style={{ pointerEvents: 'auto', cursor: isSelectionLocked ? 'not-allowed' : 'move' }}></div>
              </Rnd>
            )}
            {visibleFields.map((field) => {
              const isSelected = activeFieldIds.includes(field.id);
              const isPrimarySelection = activeFieldIds[activeFieldIds.length - 1] === field.id;
              const isPartOfSelection = activeFieldIds.length > 1 && isSelected;
              const isHovered = hoveredFieldId === field.id;

              return (
                <FormFieldComponent
                  key={field.id}
                  field={field}
                  template={template}
                  formState={formState}
                  isSelected={isSelected}
                  isPrimarySelection={isPrimarySelection}
                  isPartOfSelection={isPartOfSelection}
                  isHovered={isHovered}
                  isInteractive={isInteractive}
                  isCard={isCard}
                  inlineEditingFieldId={inlineEditingFieldId ?? null}
                  isShiftPressed={isShiftPressed}
                  onDragStart={(e) => {
                    e.stopPropagation();
                    if (isInteractive && handleFieldSelect) {
                      const isRightClick = 'button' in e && (e as React.MouseEvent).button === 2;
                      if (isRightClick) {
                        if (!activeFieldIds.includes(field.id)) handleFieldSelect(field.id, e.shiftKey);
                        return;
                      }
                      handleFieldSelect(field.id, e.shiftKey);
                    }
                  }}
                  onDrag={(data) => handleDrag(field, data)}
                  onDragStop={(data) => handleDragStop(field, data)}
                  onResizeStart={(e) => {
                    e.stopPropagation();
                    if (isInteractive && handleFieldSelect) handleFieldSelect(field.id, e.shiftKey);
                  }}
                  onResizeStop={(dir, ref, delta, pos) => {
                    if (isInteractive && updateField)
                      updateField(field.id, {
                        width: parseInt(ref.style.width, 10),
                        height: parseInt(ref.style.height, 10),
                        x: Math.round(pos.x),
                        y: Math.round(pos.y),
                      });
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (isInteractive && handleFieldSelect) handleFieldSelect(field.id, e.shiftKey);
                  }}
                  onDoubleClick={(e) => {
                    e.stopPropagation();
                    if (isInteractive && handleFieldSelect) {
                      if (field.type === 'static-text' && setInlineEditingFieldId) {
                        setInlineEditingFieldId(field.id);
                      } else {
                        handleFieldSelect(field.id, false, true);
                      }
                    }
                  }}
                  renderContextMenu={(children) => (
                    <ContextMenu>
                      <ContextMenuTrigger asChild>{children}</ContextMenuTrigger>
                      <ContextMenuContent className="w-64" onClick={(e) => e.stopPropagation()}>
                        {isInteractive && activeFieldIds.length > 0 && copyFields && (<ContextMenuItem onClick={() => copyFields(activeFieldIds)}><Copy className="mr-2 h-4 w-4" /> Copy</ContextMenuItem>)}
                        {isInteractive && clipboard && pasteFields && (<ContextMenuItem onClick={() => { const newIds = pasteFields(); if (newIds.length > 0 && setActiveFieldIds) setActiveFieldIds(newIds); }}><ClipboardPaste className="mr-2 h-4 w-4" /> Paste</ContextMenuItem>)}
                        {isInteractive && activeFieldIds.length > 0 && <ContextMenuSeparator />}
                        {isInteractive && activeFieldIds.length > 0 && duplicateFields && (<ContextMenuItem onClick={() => { const newIds = duplicateFields(activeFieldIds); if (newIds.length > 0 && setActiveFieldIds) setActiveFieldIds(newIds); }}><Copy className="mr-2 h-4 w-4" /> Duplicate</ContextMenuItem>)}
                        {isInteractive && activeFieldIds.length > 1 && (
                          <>
                            {isGroupSelection ? (<ContextMenuItem onClick={() => ungroupFields && ungroupFields(activeFieldIds)}><Ungroup className="mr-2 h-4 w-4" /> Ungroup</ContextMenuItem>) : (<ContextMenuItem onClick={() => groupFields && groupFields(activeFieldIds)}><Group className="mr-2 h-4 w-4" /> Group</ContextMenuItem>)}
                            <ContextMenuSub>
                              <ContextMenuSubTrigger><AlignLeft className="mr-2 h-4 w-4" /> Arrange</ContextMenuSubTrigger>
                              <ContextMenuSubContent>
                                <ContextMenuItem onClick={() => alignFields && alignFields('left', activeFieldIds)}><AlignLeft className="mr-2 h-4 w-4" /> Align Left</ContextMenuItem>
                                <ContextMenuItem onClick={() => alignFields && alignFields('h-center', activeFieldIds)}><AlignCenterHorizontal className="mr-2 h-4 w-4" /> Align Center (H)</ContextMenuItem>
                                <ContextMenuItem onClick={() => alignFields && alignFields('right', activeFieldIds)}><AlignRight className="mr-2 h-4 w-4" /> Align Right</ContextMenuItem>
                                <ContextMenuSeparator />
                                <ContextMenuItem onClick={() => alignFields && alignFields('top', activeFieldIds)}><AlignStartVertical className="mr-2 h-4 w-4" /> Align Top</ContextMenuItem>
                                <ContextMenuItem onClick={() => alignFields && alignFields('v-center', activeFieldIds)}><AlignCenterVertical className="mr-2 h-4 w-4" /> Align Center (V)</ContextMenuItem>
                                <ContextMenuItem onClick={() => alignFields && alignFields('bottom', activeFieldIds)}><AlignEndVertical className="mr-2 h-4 w-4" /> Align Bottom</ContextMenuItem>
                                <ContextMenuSeparator />
                                <ContextMenuItem onClick={() => distributeFields && distributeFields('horizontal', activeFieldIds)} disabled={activeFieldIds.length < 3}><AlignHorizontalSpaceAround className="mr-2 h-4 w-4" /> Distribute Horizontally</ContextMenuItem>
                                <ContextMenuItem onClick={() => distributeFields && distributeFields('vertical', activeFieldIds)} disabled={activeFieldIds.length < 3}><AlignVerticalSpaceAround className="mr-2 h-4 w-4" /> Distribute Vertically</ContextMenuItem>
                              </ContextMenuSubContent>
                            </ContextMenuSub>
                          </>
                        )}
                        {isInteractive && activeFieldIds.length > 0 && moveFieldsToPage && (
                            <>
                                <ContextMenuSeparator />
                                <ContextMenuSub>
                                    <ContextMenuSubTrigger disabled={template.pages.length <= 1}>
                                        <Move className="mr-2 h-4 w-4" /> Move to Page
                                    </ContextMenuSubTrigger>
                                    <ContextMenuSubContent>
                                        {template.pages.map((p, index) => (
                                            p.id !== page.id && (
                                                <ContextMenuItem key={p.id} onClick={() => moveFieldsToPage(activeFieldIds, p.id)}>
                                                    Page {index + 1}
                                                </ContextMenuItem>
                                            )
                                        ))}
                                    </ContextMenuSubContent>
                                </ContextMenuSub>
                            </>
                        )}
                        {isInteractive && activeFieldIds.length === 1 && (
                          <>
                            <ContextMenuSeparator />
                            <ContextMenuSub>
                              <ContextMenuSubTrigger><ArrowUp className="mr-2 h-4 w-4" /> Order</ContextMenuSubTrigger>
                              <ContextMenuSubContent>
                                <ContextMenuItem onClick={() => bringToFront && bringToFront(field.id)}><ChevronsUp className="mr-2 h-4 w-4" /> Bring to Front</ContextMenuItem>
                                <ContextMenuItem onClick={() => bringForward && bringForward(field.id)}><ArrowUp className="mr-2 h-4 w-4" /> Bring Forward</ContextMenuItem>
                                <ContextMenuItem onClick={() => sendBackward && sendBackward(field.id)}><ArrowDown className="mr-2 h-4 w-4" /> Send Backward</ContextMenuItem>
                                <ContextMenuItem onClick={() => sendToBack && sendToBack(field.id)}><ChevronsDown className="mr-2 h-4 w-4" /> Send to Back</ContextMenuItem>
                              </ContextMenuSubContent>
                            </ContextMenuSub>
                          </>
                        )}
                        {isInteractive && activeFieldIds.length > 0 && deleteFields && (
                          <>
                            <ContextMenuSeparator />
                            <ContextMenuItem className="text-red-600" onClick={() => deleteFields(activeFieldIds)}><Trash2 className="mr-2 h-4 w-4" /> Delete</ContextMenuItem>
                          </>
                        )}
                      </ContextMenuContent>
                    </ContextMenu>
                  )}
                />
              );
            })}
            {marquee && <div style={marqueeStyle} />}
            {activeSnapLines.map((guide, index) => (
              <div
                key={index}
                className="absolute bg-red-500"
                style={
                  guide.direction === "vertical"
                    ? { left: guide.position, top: guide.start, width: "1px", height: guide.end - guide.start }
                    : { top: guide.position, left: guide.start, height: "1px", width: guide.end - guide.start }
                }
              />
            ))}
          </div>
          {!isCard && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 text-xs text-gray-400">
              Page {pageIndex + 1}
            </div>
          )}
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent className="w-64" onClick={(e) => e.stopPropagation()}>
        {isInteractive && clipboard && pasteFields && (
          <ContextMenuItem onClick={() => { const newIds = pasteFields(); if (newIds.length > 0 && setActiveFieldIds) setActiveFieldIds(newIds); }}>
            <ClipboardPaste className="mr-2 h-4 w-4" /> Paste
          </ContextMenuItem>
        )}
        {isInteractive && template && template.pages.flatMap(p => p.fields).length > 0 && (
          <ContextMenuItem onClick={handleSelectAll}>
            <BoxSelect className="mr-2 h-4 w-4" /> Select All
          </ContextMenuItem>
        )}
        {isInteractive && activeFieldIds.length > 0 && (
          <ContextMenuItem onClick={() => setActiveFieldIds && setActiveFieldIds([])}>
            Deselect All
          </ContextMenuItem>
        )}
      </ContextMenuContent>
    </ContextMenu>
  );
};