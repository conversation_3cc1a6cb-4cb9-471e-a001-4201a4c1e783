import { useState, useCallback } from 'react';

export const useHistory = <T>(initialState: T) => {
  const [history, setHistory] = useState<T[]>([initialState]);
  const [index, setIndex] = useState(0);

  const setState = useCallback((newState: T | ((prevState: T) => T)) => {
    setHistory(currentHistory => {
      const newHistory = currentHistory.slice(0, index + 1);
      const stateToSet = typeof newState === 'function' 
        ? (newState as (prevState: T) => T)(newHistory[index]) 
        : newState;
      
      if (JSON.stringify(stateToSet) === JSON.stringify(newHistory[index])) {
        return currentHistory;
      }

      newHistory.push(stateToSet);
      setIndex(newHistory.length - 1);
      return newHistory;
    });
  }, [index]);

  const undo = useCallback(() => {
    if (index > 0) {
      setIndex(prevIndex => prevIndex - 1);
    }
  }, [index]);

  const redo = useCallback(() => {
    if (index < history.length - 1) {
      setIndex(prevIndex => prevIndex + 1);
    }
  }, [index, history.length]);

  const resetHistory = useCallback((state: T) => {
    setHistory([state]);
    setIndex(0);
  }, []);

  const canUndo = index > 0;
  const canRedo = index < history.length - 1;

  return [history[index], setState, undo, redo, canUndo, canRedo, resetHistory] as const;
};