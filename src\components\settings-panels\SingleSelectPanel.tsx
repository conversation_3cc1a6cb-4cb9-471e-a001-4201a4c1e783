import { FormField } from "@/data/templates";
import { Accordion } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Trash2 } from "lucide-react";
import { useTemplateContext } from "@/context/TemplateContext";
import { GeneralPanel } from "./GeneralPanel";
import { LayoutPanel } from "./LayoutPanel";
import { OptionsPanel } from "./OptionsPanel";
import { StylingPanel } from "./StylingPanel";
import { TablePanel } from "./TablePanel";
import { ValidationPanel } from "./ValidationPanel";
import { VisibilityPanel } from "./VisibilityPanel";
import { BoxStylingPanel } from "./BoxStylingPanel";
import { LineStylingPanel } from "./LineStylingPanel";
import { ArrowStylingPanel } from "./ArrowStylingPanel";
import { QRCodePanel } from "./QRCodePanel";

interface SingleSelectPanelProps {
  field: FormField;
}

export const SingleSelectPanel = ({ field }: SingleSelectPanelProps) => {
  const { deleteFields } = useTemplateContext();

  return (
    <div className="p-4 space-y-4">
      <h3 className="font-semibold text-lg">Field Settings</h3>
      <Accordion type="multiple" defaultValue={["general", "layout"]}>
        <GeneralPanel field={field} />
        {(field.type === "select" || field.type === "radio") && (
          <OptionsPanel field={field} />
        )}
        {field.type === "static-text" && <StylingPanel field={field} />}
        {field.type === "box" && <BoxStylingPanel field={field} />}
        {field.type === "line" && <LineStylingPanel field={field} />}
        {field.type === "arrow" && <ArrowStylingPanel field={field} />}
        {field.type === "table" && <TablePanel field={field} />}
        {field.type === "qrcode" && <QRCodePanel field={field} />}
        <LayoutPanel field={field} />
        <ValidationPanel field={field} />
        <VisibilityPanel field={field} />
      </Accordion>
      <Separator />
      <Button
        variant="destructive"
        className="w-full"
        onClick={() => deleteFields([field.id])}
      >
        <Trash2 className="mr-2 h-4 w-4" />
        Delete Field
      </Button>
    </div>
  );
};