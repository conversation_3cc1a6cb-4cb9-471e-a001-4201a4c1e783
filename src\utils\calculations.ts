import { FormField } from "@/data/templates";

export const calculateTableSummary = (
  tableField: FormField,
  tableRows: Record<string, any>[]
): { label: string; value: string }[] => {
  if (!tableField.summary || !tableRows) {
    return [];
  }

  const summaryValues: Record<string, number> = {};

  // First pass: Handle SUM aggregations
  tableField.summary.forEach(item => {
    const sumMatch = item.formula.match(/SUM\(([^)]+)\)/);
    if (sumMatch) {
      const columnToSum = sumMatch[1];
      const sum = tableRows.reduce((acc, row) => {
        const value = parseFloat(row[columnToSum]);
        return acc + (isNaN(value) ? 0 : value);
      }, 0);
      summaryValues[item.id] = sum;
    }
  });

  // Second pass: Handle other calculations which may depend on SUMs
  tableField.summary.forEach(item => {
    if (!summaryValues.hasOwnProperty(item.id)) {
      const formula = item.formula;
      const populatedFormula = formula.replace(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g, (match) => {
        const value = summaryValues[match];
        return typeof value === 'number' ? value.toString() : '0';
      });

      try {
        const result = new Function(`return ${populatedFormula}`)();
        if (typeof result === 'number' && isFinite(result)) {
          summaryValues[item.id] = result;
        }
      } catch (e) {
        console.error(`Error calculating summary for ${item.id}:`, e);
      }
    }
  });

  return tableField.summary.map(item => ({
    label: item.label,
    value: (summaryValues[item.id] ?? 0).toFixed(2),
  }));
};