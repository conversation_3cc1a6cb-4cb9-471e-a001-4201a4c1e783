import { But<PERSON> } from "@/components/ui/button";
import { ZoomIn, ZoomOut, Expand } from "lucide-react";
import { useEditorUIContext } from "@/context/EditorUIContext";

interface ZoomControlsProps {
  onFitToScreen: () => void;
}

export const ZoomControls: React.FC<ZoomControlsProps> = ({ onFitToScreen }) => {
  const { zoomLevel, setZoomLevel } = useEditorUIContext();
  return (
    <div className="absolute bottom-4 right-4 bg-card p-2 rounded-lg shadow-lg border flex flex-col gap-1 z-30">
      <Button variant="ghost" size="icon" onClick={() => setZoomLevel(Math.min(4, zoomLevel + 0.1))}><ZoomIn className="h-5 w-5" /></Button>
      <Button variant="ghost" size="icon" onClick={onFitToScreen}><Expand className="h-5 w-5" /></Button>
      <span className="text-xs text-center font-mono">{Math.round(zoomLevel * 100)}%</span>
      <Button variant="ghost" size="icon" onClick={() => setZoomLevel(Math.max(0.2, zoomLevel - 0.1))}><ZoomOut className="h-5 w-5" /></Button>
    </div>
  );
};