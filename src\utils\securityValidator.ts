import { fileTypeFrom<PERSON>uffer } from 'file-type';

export interface SecurityValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedContent?: string;
  metadata: {
    fileType: string;
    fileSize: number;
    detectedMimeType?: string;
    hasExecutableContent: boolean;
    hasSuspiciousPatterns: boolean;
  };
}

export interface FileValidationOptions {
  maxFileSize: number; // in bytes
  allowedMimeTypes: string[];
  allowedExtensions: string[];
  scanForMalware: boolean;
  sanitizeContent: boolean;
  checkFileIntegrity: boolean;
}

/**
 * Security validator for uploaded files and content
 */
export class SecurityValidator {
  private defaultOptions: FileValidationOptions = {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/pdf'
    ],
    allowedExtensions: ['.docx', '.pdf'],
    scanForMalware: true,
    sanitizeContent: true,
    checkFileIntegrity: true
  };

  // Suspicious patterns that might indicate malicious content
  private suspiciousPatterns = [
    // Script injection patterns
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /data:text\/html/gi,
    /data:application\/javascript/gi,
    
    // Command injection patterns
    /\$\(.*\)/g,
    /`.*`/g,
    /\|\s*[a-zA-Z]/g,
    
    // File system access patterns
    /\.\.\/|\.\.\\|\.\.\//g,
    /\/etc\/passwd/gi,
    /\/proc\//gi,
    /C:\\Windows/gi,
    
    // Network access patterns
    /http:\/\/[^\/\s]+/gi,
    /https:\/\/[^\/\s]+/gi,
    /ftp:\/\/[^\/\s]+/gi,
    
    // Executable file patterns
    /\.exe\b/gi,
    /\.bat\b/gi,
    /\.cmd\b/gi,
    /\.scr\b/gi,
    /\.com\b/gi,
    /\.pif\b/gi,
    
    // Macro patterns (for Office documents)
    /Auto_Open/gi,
    /Workbook_Open/gi,
    /Document_Open/gi,
    /Shell\(/gi,
    /CreateObject\(/gi
  ];

  // Dangerous file signatures (magic bytes)
  private dangerousSignatures = [
    { signature: [0x4D, 0x5A], description: 'PE executable' },
    { signature: [0x7F, 0x45, 0x4C, 0x46], description: 'ELF executable' },
    { signature: [0xCA, 0xFE, 0xBA, 0xBE], description: 'Java class file' },
    { signature: [0xFE, 0xED, 0xFA, 0xCE], description: 'Mach-O executable' },
    { signature: [0x89, 0x50, 0x4E, 0x47], description: 'PNG (could contain malicious data)' }
  ];

  /**
   * Validate uploaded file for security threats
   */
  async validateFile(file: File, options: Partial<FileValidationOptions> = {}): Promise<SecurityValidationResult> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const errors: string[] = [];
    const warnings: string[] = [];
    
    try {
      // Basic file validation
      this.validateBasicFileProperties(file, mergedOptions, errors, warnings);
      
      // Read file buffer for advanced validation
      const buffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(buffer);
      
      // Validate file type by magic bytes
      await this.validateFileType(uint8Array, file.name, mergedOptions, errors, warnings);
      
      // Check for dangerous file signatures
      this.checkDangerousSignatures(uint8Array, warnings);
      
      // Scan file content if it's a text-based format
      let sanitizedContent: string | undefined;
      if (mergedOptions.sanitizeContent) {
        sanitizedContent = await this.scanAndSanitizeContent(file, errors, warnings);
      }
      
      // Check file integrity
      if (mergedOptions.checkFileIntegrity) {
        await this.validateFileIntegrity(uint8Array, file.type, errors, warnings);
      }
      
      const metadata = {
        fileType: file.type,
        fileSize: file.size,
        detectedMimeType: await this.detectMimeType(uint8Array),
        hasExecutableContent: this.hasExecutableContent(uint8Array),
        hasSuspiciousPatterns: sanitizedContent ? this.hasSuspiciousPatterns(sanitizedContent) : false
      };
      
      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        sanitizedContent,
        metadata
      };
      
    } catch (error) {
      errors.push(`Security validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      return {
        isValid: false,
        errors,
        warnings,
        metadata: {
          fileType: file.type,
          fileSize: file.size,
          hasExecutableContent: false,
          hasSuspiciousPatterns: false
        }
      };
    }
  }

  /**
   * Validate basic file properties
   */
  private validateBasicFileProperties(
    file: File,
    options: FileValidationOptions,
    errors: string[],
    warnings: string[]
  ): void {
    // Check file size
    if (file.size > options.maxFileSize) {
      errors.push(`File size (${Math.round(file.size / 1024 / 1024)}MB) exceeds maximum allowed size (${Math.round(options.maxFileSize / 1024 / 1024)}MB)`);
    }
    
    if (file.size === 0) {
      errors.push('File is empty');
    }
    
    // Check file extension
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!options.allowedExtensions.includes(extension)) {
      errors.push(`File extension "${extension}" is not allowed. Allowed extensions: ${options.allowedExtensions.join(', ')}`);
    }
    
    // Check MIME type
    if (!options.allowedMimeTypes.includes(file.type)) {
      warnings.push(`MIME type "${file.type}" is not in the allowed list. This might indicate a file type mismatch.`);
    }
    
    // Check for suspicious file names
    if (this.hasSuspiciousFileName(file.name)) {
      warnings.push('File name contains suspicious patterns');
    }
  }

  /**
   * Validate file type using magic bytes
   */
  private async validateFileType(
    buffer: Uint8Array,
    fileName: string,
    options: FileValidationOptions,
    errors: string[],
    warnings: string[]
  ): Promise<void> {
    try {
      const detectedType = await fileTypeFromBuffer(buffer);
      
      if (!detectedType) {
        warnings.push('Could not detect file type from content');
        return;
      }
      
      // Check if detected type matches allowed types
      const detectedMimeType = detectedType.mime;
      if (!options.allowedMimeTypes.includes(detectedMimeType)) {
        errors.push(`Detected file type "${detectedMimeType}" is not allowed`);
      }
      
      // Check for file type spoofing
      const expectedExtension = '.' + fileName.split('.').pop()?.toLowerCase();
      if (detectedType.ext !== expectedExtension.substring(1)) {
        warnings.push(`File extension "${expectedExtension}" does not match detected type "${detectedType.ext}"`);
      }
      
    } catch (error) {
      warnings.push('Failed to detect file type from content');
    }
  }

  /**
   * Check for dangerous file signatures
   */
  private checkDangerousSignatures(buffer: Uint8Array, warnings: string[]): void {
    for (const { signature, description } of this.dangerousSignatures) {
      if (this.matchesSignature(buffer, signature)) {
        warnings.push(`File contains potentially dangerous signature: ${description}`);
      }
    }
  }

  /**
   * Scan and sanitize file content
   */
  private async scanAndSanitizeContent(
    file: File,
    errors: string[],
    warnings: string[]
  ): Promise<string | undefined> {
    try {
      // For now, we'll only scan text content from the file name and basic metadata
      // In a full implementation, this would extract and scan the actual document content
      const textContent = file.name + ' ' + file.type;
      
      // Check for suspicious patterns
      for (const pattern of this.suspiciousPatterns) {
        if (pattern.test(textContent)) {
          warnings.push(`Suspicious pattern detected in file metadata`);
        }
      }
      
      // Sanitize content by removing potentially dangerous patterns
      let sanitized = textContent;
      for (const pattern of this.suspiciousPatterns) {
        sanitized = sanitized.replace(pattern, '[REMOVED]');
      }
      
      return sanitized;
      
    } catch (error) {
      warnings.push('Failed to scan file content');
      return undefined;
    }
  }

  /**
   * Validate file integrity
   */
  private async validateFileIntegrity(
    buffer: Uint8Array,
    mimeType: string,
    errors: string[],
    warnings: string[]
  ): Promise<void> {
    try {
      // Basic integrity checks based on file type
      if (mimeType === 'application/pdf') {
        // Check PDF header
        const pdfHeader = new TextDecoder().decode(buffer.slice(0, 4));
        if (pdfHeader !== '%PDF') {
          errors.push('Invalid PDF file: missing PDF header');
        }
      } else if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        // Check ZIP signature (DOCX files are ZIP archives)
        if (buffer[0] !== 0x50 || buffer[1] !== 0x4B) {
          errors.push('Invalid DOCX file: missing ZIP signature');
        }
      }
      
      // Check for truncated files
      if (buffer.length < 100) {
        warnings.push('File appears to be very small or truncated');
      }
      
    } catch (error) {
      warnings.push('Failed to validate file integrity');
    }
  }

  /**
   * Detect MIME type from buffer
   */
  private async detectMimeType(buffer: Uint8Array): Promise<string | undefined> {
    try {
      const detectedType = await fileTypeFromBuffer(buffer);
      return detectedType?.mime;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Check if file has executable content
   */
  private hasExecutableContent(buffer: Uint8Array): boolean {
    // Check for common executable signatures
    for (const { signature } of this.dangerousSignatures) {
      if (this.matchesSignature(buffer, signature)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if content has suspicious patterns
   */
  private hasSuspiciousPatterns(content: string): boolean {
    return this.suspiciousPatterns.some(pattern => pattern.test(content));
  }

  /**
   * Check if filename is suspicious
   */
  private hasSuspiciousFileName(fileName: string): boolean {
    const suspiciousNamePatterns = [
      /\.(exe|bat|cmd|scr|com|pif)$/i,
      /\.\w+\.(exe|bat|cmd)$/i, // Double extension
      /[<>:"|?*]/,              // Invalid filename characters
      /^\./,                    // Hidden files
      /\s+$/,                   // Trailing spaces
      /.{255,}/                 // Very long names
    ];
    
    return suspiciousNamePatterns.some(pattern => pattern.test(fileName));
  }

  /**
   * Check if buffer matches a signature
   */
  private matchesSignature(buffer: Uint8Array, signature: number[]): boolean {
    if (buffer.length < signature.length) {
      return false;
    }
    
    for (let i = 0; i < signature.length; i++) {
      if (buffer[i] !== signature[i]) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Sanitize text content by removing dangerous patterns
   */
  sanitizeTextContent(content: string): string {
    let sanitized = content;
    
    // Remove script tags and their content
    sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gis, '');
    
    // Remove javascript: and vbscript: protocols
    sanitized = sanitized.replace(/javascript:|vbscript:/gi, '');
    
    // Remove data URLs that could contain executable content
    sanitized = sanitized.replace(/data:(?:text\/html|application\/javascript)[^;]*;[^,]*,/gi, '');
    
    // Remove potential command injection patterns
    sanitized = sanitized.replace(/\$\([^)]*\)/g, '');
    sanitized = sanitized.replace(/`[^`]*`/g, '');
    
    // Remove file system traversal patterns
    sanitized = sanitized.replace(/\.\.\/|\.\.\\|\.\.\//g, '');
    
    return sanitized;
  }

  /**
   * Generate security report
   */
  generateSecurityReport(validationResult: SecurityValidationResult): string {
    const report = [];
    
    report.push('=== SECURITY VALIDATION REPORT ===');
    report.push(`Status: ${validationResult.isValid ? 'PASSED' : 'FAILED'}`);
    report.push(`File Type: ${validationResult.metadata.fileType}`);
    report.push(`File Size: ${Math.round(validationResult.metadata.fileSize / 1024)}KB`);
    
    if (validationResult.metadata.detectedMimeType) {
      report.push(`Detected MIME Type: ${validationResult.metadata.detectedMimeType}`);
    }
    
    report.push(`Has Executable Content: ${validationResult.metadata.hasExecutableContent ? 'YES' : 'NO'}`);
    report.push(`Has Suspicious Patterns: ${validationResult.metadata.hasSuspiciousPatterns ? 'YES' : 'NO'}`);
    
    if (validationResult.errors.length > 0) {
      report.push('\n=== ERRORS ===');
      validationResult.errors.forEach((error, index) => {
        report.push(`${index + 1}. ${error}`);
      });
    }
    
    if (validationResult.warnings.length > 0) {
      report.push('\n=== WARNINGS ===');
      validationResult.warnings.forEach((warning, index) => {
        report.push(`${index + 1}. ${warning}`);
      });
    }
    
    return report.join('\n');
  }
}
