import { FormField, Template } from '@/data/templates';
import { DetectedPlaceholder } from './placeholderDetection';

export interface PlaceholderMapping {
  placeholderId: string;
  fieldId: string;
  originalText: string;
  mappedName: string;
  fieldType: FormField['type'];
  position?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  isActive: boolean;
}

export interface PlaceholderValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Manages placeholder mappings and integrates with the PDF editor
 */
export class PlaceholderManager {
  private mappings: Map<string, PlaceholderMapping> = new Map();
  private template: Template;

  constructor(template: Template) {
    this.template = template;
    this.initializeMappings();
  }

  /**
   * Initialize mappings from existing template fields
   */
  private initializeMappings(): void {
    this.template.pages.forEach(page => {
      page.fields.forEach(field => {
        const mapping: PlaceholderMapping = {
          placeholderId: field.id,
          fieldId: field.id,
          originalText: field.placeholder || field.label,
          mappedName: field.label,
          fieldType: field.type,
          position: {
            x: field.x,
            y: field.y,
            width: field.width,
            height: field.height
          },
          isActive: true
        };
        this.mappings.set(field.id, mapping);
      });
    });
  }

  /**
   * Add new placeholder mapping
   */
  addPlaceholderMapping(placeholder: DetectedPlaceholder, position?: PlaceholderMapping['position']): string {
    const mapping: PlaceholderMapping = {
      placeholderId: placeholder.id,
      fieldId: `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      originalText: placeholder.text,
      mappedName: placeholder.suggestedName,
      fieldType: placeholder.suggestedType,
      position: position || this.calculateDefaultPosition(),
      isActive: true
    };

    this.mappings.set(placeholder.id, mapping);
    return mapping.fieldId;
  }

  /**
   * Update existing placeholder mapping
   */
  updatePlaceholderMapping(placeholderId: string, updates: Partial<PlaceholderMapping>): boolean {
    const existing = this.mappings.get(placeholderId);
    if (!existing) return false;

    const updated = { ...existing, ...updates };
    this.mappings.set(placeholderId, updated);
    return true;
  }

  /**
   * Remove placeholder mapping
   */
  removePlaceholderMapping(placeholderId: string): boolean {
    return this.mappings.delete(placeholderId);
  }

  /**
   * Get all active mappings
   */
  getActiveMappings(): PlaceholderMapping[] {
    return Array.from(this.mappings.values()).filter(mapping => mapping.isActive);
  }

  /**
   * Get mapping by placeholder ID
   */
  getMapping(placeholderId: string): PlaceholderMapping | undefined {
    return this.mappings.get(placeholderId);
  }

  /**
   * Bulk rename placeholders
   */
  bulkRename(pattern: string, replacement: string): number {
    let updatedCount = 0;
    const regex = new RegExp(pattern, 'gi');

    this.mappings.forEach((mapping, id) => {
      if (regex.test(mapping.mappedName)) {
        const newName = mapping.mappedName.replace(regex, replacement);
        this.updatePlaceholderMapping(id, { mappedName: newName });
        updatedCount++;
      }
    });

    return updatedCount;
  }

  /**
   * Validate all placeholder mappings
   */
  validateMappings(): PlaceholderValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const usedNames = new Set<string>();

    this.mappings.forEach((mapping, id) => {
      // Check for duplicate names
      if (usedNames.has(mapping.mappedName)) {
        errors.push(`Duplicate field name: "${mapping.mappedName}"`);
      } else {
        usedNames.add(mapping.mappedName);
      }

      // Check for empty names
      if (!mapping.mappedName.trim()) {
        errors.push(`Empty field name for placeholder: "${mapping.originalText}"`);
      }

      // Check for invalid characters in names
      if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(mapping.mappedName)) {
        warnings.push(`Field name "${mapping.mappedName}" contains invalid characters. Use only letters, numbers, and underscores.`);
      }

      // Check position validity
      if (mapping.position) {
        if (mapping.position.x < 0 || mapping.position.y < 0) {
          warnings.push(`Field "${mapping.mappedName}" has negative position coordinates`);
        }
        if (mapping.position.width <= 0 || mapping.position.height <= 0) {
          warnings.push(`Field "${mapping.mappedName}" has invalid dimensions`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Convert mappings to template fields
   */
  convertToTemplateFields(): FormField[] {
    const fields: FormField[] = [];

    this.mappings.forEach(mapping => {
      if (!mapping.isActive || !mapping.position) return;

      const field: FormField = {
        id: mapping.fieldId,
        label: this.formatFieldLabel(mapping.mappedName),
        type: mapping.fieldType,
        x: mapping.position.x,
        y: mapping.position.y,
        width: mapping.position.width,
        height: mapping.position.height,
        placeholder: this.generatePlaceholderText(mapping.mappedName, mapping.fieldType),
        validation: this.generateValidationRules(mapping.fieldType)
      };

      // Add type-specific properties
      if (mapping.fieldType === 'select') {
        field.options = this.generateSelectOptions(mapping.originalText);
      }

      fields.push(field);
    });

    return fields;
  }

  /**
   * Apply mappings to template
   */
  applyToTemplate(): Template {
    const fields = this.convertToTemplateFields();
    
    // Update the first page with new fields
    const updatedPages = [...this.template.pages];
    if (updatedPages.length > 0) {
      updatedPages[0] = {
        ...updatedPages[0],
        fields: [...updatedPages[0].fields, ...fields]
      };
    } else {
      updatedPages.push({
        id: `page_${Date.now()}`,
        fields
      });
    }

    return {
      ...this.template,
      pages: updatedPages
    };
  }

  /**
   * Calculate default position for new fields
   */
  private calculateDefaultPosition(): PlaceholderMapping['position'] {
    const existingFields = this.convertToTemplateFields();
    let maxY = 100; // Start position

    existingFields.forEach(field => {
      maxY = Math.max(maxY, field.y + field.height + 20);
    });

    return {
      x: 40,
      y: maxY,
      width: 200,
      height: 30
    };
  }

  /**
   * Format field label for display
   */
  private formatFieldLabel(name: string): string {
    return name
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  /**
   * Generate placeholder text for field
   */
  private generatePlaceholderText(name: string, type: FormField['type']): string {
    const formattedName = this.formatFieldLabel(name);
    
    switch (type) {
      case 'date': return 'Select date...';
      case 'number': return 'Enter number...';
      case 'email': return 'Enter email address...';
      case 'textarea': return `Enter ${formattedName.toLowerCase()}...`;
      case 'signature': return 'Click to sign...';
      case 'select': return 'Select option...';
      default: return `Enter ${formattedName.toLowerCase()}...`;
    }
  }

  /**
   * Generate validation rules based on field type
   */
  private generateValidationRules(type: FormField['type']): FormField['validation'] {
    switch (type) {
      case 'email':
        return { required: true };
      case 'number':
        return { required: false, minValue: 0 };
      case 'textarea':
        return { maxLength: 1000 };
      case 'text':
        return { maxLength: 255 };
      default:
        return {};
    }
  }

  /**
   * Generate select options from context
   */
  private generateSelectOptions(originalText: string): string[] {
    // Basic options based on common railway terms
    const railwayOptions = {
      division: ['Northern Railway', 'Southern Railway', 'Eastern Railway', 'Western Railway', 'Central Railway'],
      status: ['Active', 'Inactive', 'Under Maintenance', 'Out of Service'],
      priority: ['High', 'Medium', 'Low'],
      type: ['Passenger', 'Freight', 'Express', 'Local']
    };

    const lowerText = originalText.toLowerCase();
    
    for (const [key, options] of Object.entries(railwayOptions)) {
      if (lowerText.includes(key)) {
        return options;
      }
    }

    return ['Option 1', 'Option 2', 'Option 3'];
  }

  /**
   * Export mappings for persistence
   */
  exportMappings(): any {
    return {
      mappings: Array.from(this.mappings.entries()),
      templateId: this.template.title,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Import mappings from saved data
   */
  importMappings(data: any): boolean {
    try {
      if (data.mappings && Array.isArray(data.mappings)) {
        this.mappings.clear();
        data.mappings.forEach(([id, mapping]: [string, PlaceholderMapping]) => {
          this.mappings.set(id, mapping);
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to import mappings:', error);
      return false;
    }
  }
}
