import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTemplateContext } from "@/context/TemplateContext";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";

const themes = [
  { name: "Default", colors: { primaryColor: "#111827", textColor: "#374151", accentColor: "#e5e7eb" } },
  { name: "Ocean", colors: { primaryColor: "#0c4a6e", textColor: "#1e293b", accentColor: "#bae6fd" } },
  { name: "Forest", colors: { primaryColor: "#14532d", textColor: "#1f2937", accentColor: "#d1fae5" } },
  { name: "<PERSON>", colors: { primaryColor: "#831843", textColor: "#44403c", accentColor: "#fce7f3" } },
];

export const DesignPanel = () => {
  const { template, updateDesignSettings } = useTemplateContext();
  const design = template.designSettings;

  return (
    <Card>
      <CardHeader><CardTitle>Design Settings</CardTitle><CardDescription>Customize the look and feel of your document.</CardDescription></CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>Color Themes</Label>
          <div className="grid grid-cols-2 gap-2">
            {themes.map((theme) => (
              <button key={theme.name} onClick={() => updateDesignSettings(theme.colors)} className="p-2 border rounded-md hover:border-primary text-left">
                <div className="flex items-center gap-2">
                  <div className="flex -space-x-2">
                    <div className="w-5 h-5 rounded-full border-2 border-background" style={{ backgroundColor: theme.colors.primaryColor }} />
                    <div className="w-5 h-5 rounded-full border-2 border-background" style={{ backgroundColor: theme.colors.textColor }} />
                    <div className="w-5 h-5 rounded-full border-2 border-background" style={{ backgroundColor: theme.colors.accentColor }} />
                  </div>
                  <span className="text-sm font-medium">{theme.name}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
        <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
          <div className="space-y-0.5">
            <Label>Show Title</Label>
            <p className="text-xs text-muted-foreground">
              Display the main title on the document.
            </p>
          </div>
          <Switch
            checked={design?.showTitle ?? true}
            onCheckedChange={(checked) => updateDesignSettings({ showTitle: checked })}
          />
        </div>
        <div className="space-y-2 pt-4 border-t">
          <Label htmlFor="font-family">Font Family</Label>
          <Select value={design?.fontFamily || "Helvetica"} onValueChange={(value) => updateDesignSettings({ fontFamily: value })}>
            <SelectTrigger id="font-family"><SelectValue placeholder="Select a font" /></SelectTrigger>
            <SelectContent><SelectItem value="Helvetica">Helvetica (Sans-Serif)</SelectItem><SelectItem value="Times New Roman">Times New Roman (Serif)</SelectItem><SelectItem value="Courier New">Courier New (Monospace)</SelectItem></SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="primary-color">Primary Color</Label>
          <div className="flex items-center gap-2"><Input id="primary-color" type="color" value={design?.primaryColor || "#000000"} onChange={(e) => updateDesignSettings({ primaryColor: e.target.value })} className="p-1 h-10 w-14" /><Input type="text" value={design?.primaryColor || "#000000"} onChange={(e) => updateDesignSettings({ primaryColor: e.target.value })} className="font-mono" /></div>
          <p className="text-xs text-muted-foreground">Used for main headings and titles.</p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="text-color">Text Color</Label>
          <div className="flex items-center gap-2"><Input id="text-color" type="color" value={design?.textColor || "#333333"} onChange={(e) => updateDesignSettings({ textColor: e.target.value })} className="p-1 h-10 w-14" /><Input type="text" value={design?.textColor || "#333333"} onChange={(e) => updateDesignSettings({ textColor: e.target.value })} className="font-mono" /></div>
          <p className="text-xs text-muted-foreground">Used for body copy and field inputs.</p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="accent-color">Accent Color</Label>
          <div className="flex items-center gap-2"><Input id="accent-color" type="color" value={design?.accentColor || "#dddddd"} onChange={(e) => updateDesignSettings({ accentColor: e.target.value })} className="p-1 h-10 w-14" /><Input type="text" value={design?.accentColor || "#dddddd"} onChange={(e) => updateDesignSettings({ accentColor: e.target.value })} className="font-mono" /></div>
          <p className="text-xs text-muted-foreground">Used for borders and subtle highlights.</p>
        </div>
      </CardContent>
    </Card>
  );
};