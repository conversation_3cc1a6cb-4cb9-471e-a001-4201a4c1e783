import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTemplateContext } from "@/context/TemplateContext";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";

export const WatermarkPanel = () => {
  const { template, updateWatermarkSettings } = useTemplateContext();
  const watermark = template.watermark;

  const handleEnabledChange = (enabled: boolean) => {
    updateWatermarkSettings({ enabled });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Watermark</CardTitle>
        <CardDescription>Add a text watermark to your document.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
          <div className="space-y-0.5">
            <Label>Enable Watermark</Label>
          </div>
          <Switch
            checked={watermark?.enabled || false}
            onCheckedChange={handleEnabledChange}
          />
        </div>
        {watermark?.enabled && (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="watermark-text">Text</Label>
              <Input
                id="watermark-text"
                value={watermark?.text || "DRAFT"}
                onChange={(e) => updateWatermarkSettings({ text: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="watermark-color">Color</Label>
                <Input
                  id="watermark-color"
                  type="color"
                  value={watermark?.color || "#cccccc"}
                  onChange={(e) => updateWatermarkSettings({ color: e.target.value })}
                  className="p-1 h-10 w-full"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="watermark-fontSize">Font Size</Label>
                <Input
                  id="watermark-fontSize"
                  type="number"
                  value={watermark?.fontSize || 100}
                  onChange={(e) => updateWatermarkSettings({ fontSize: parseInt(e.target.value, 10) || 100 })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label>Opacity ({Math.round((watermark?.opacity || 0.5) * 100)}%)</Label>
              <Slider
                value={[watermark?.opacity || 0.5]}
                onValueChange={(v) => updateWatermarkSettings({ opacity: v[0] })}
                min={0}
                max={1}
                step={0.05}
              />
            </div>
            <div className="space-y-2">
              <Label>Rotation ({watermark?.rotation || -45}°)</Label>
              <Slider
                value={[watermark?.rotation || -45]}
                onValueChange={(v) => updateWatermarkSettings({ rotation: v[0] })}
                min={-90}
                max={90}
                step={5}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};