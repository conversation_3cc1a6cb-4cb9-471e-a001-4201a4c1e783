import React, { useRef, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TemplateUploadCardProps {
  onUpload?: (file: File) => void;
  onClick?: () => void;
  isProcessing?: boolean;
  className?: string;
}

export const TemplateUploadCard: React.FC<TemplateUploadCardProps> = ({
  onUpload,
  onClick,
  isProcessing = false,
  className
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const handleClick = () => {
    if (!isProcessing) {
      if (onClick) {
        onClick();
      } else {
        inputRef.current?.click();
      }
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
    // Reset input value to allow re-uploading the same file
    event.target.value = '';
  };

  const handleFileUpload = (file: File) => {
    if (!onUpload) return;

    setUploadError(null);

    // Validate file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/pdf'
    ];
    const allowedExtensions = ['.docx', '.pdf'];

    const hasValidType = allowedTypes.includes(file.type);
    const hasValidExtension = allowedExtensions.some(ext =>
      file.name.toLowerCase().endsWith(ext)
    );

    if (!hasValidType && !hasValidExtension) {
      setUploadError('Please upload a DOCX or PDF file');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setUploadError('File size must be less than 10MB');
      return;
    }

    onUpload(file);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(event.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  return (
    <Card
      className={cn(
        "overflow-hidden h-full flex flex-col group cursor-pointer transition-all duration-300 hover:shadow-xl",
        "border-2 border-dashed",
        isDragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-primary",
        isProcessing && "cursor-not-allowed opacity-75",
        className
      )}
      onClick={handleClick}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <CardContent className="p-6 flex-grow flex flex-col items-center justify-center text-center">
        {isProcessing ? (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4" />
            <h3 className="font-semibold mb-2">Processing Template...</h3>
            <p className="text-sm text-muted-foreground">
              Analyzing document and detecting placeholders
            </p>
          </>
        ) : (
          <>
            <Upload className={cn(
              "h-12 w-12 mb-4 transition-colors",
              isDragOver ? "text-primary" : "text-muted-foreground group-hover:text-primary"
            )} />
            <h3 className="font-semibold mb-2">Upload Template</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Drop a DOCX or PDF file here, or click to browse
            </p>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <FileText className="h-4 w-4" />
              <span>Supports .docx and .pdf files up to 10MB</span>
            </div>
          </>
        )}
        
        {uploadError && (
          <div className="mt-4 flex items-center gap-2 text-sm text-destructive">
            <AlertCircle className="h-4 w-4" />
            <span>{uploadError}</span>
          </div>
        )}
        
        <input
          type="file"
          ref={inputRef}
          className="hidden"
          accept=".docx,.pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf"
          onChange={handleFileChange}
          disabled={isProcessing}
        />
      </CardContent>
    </Card>
  );
};
