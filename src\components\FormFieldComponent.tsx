import React, { useEffect, useRef } from "react";
import { Rnd, DraggableData } from "react-rnd";
import { FormField as FormFieldType, Template, defaultDesignSettings } from "@/data/templates";
import { cn } from "@/lib/utils";
import { calculateTableSummary } from "@/utils/calculations";
import { Check } from "lucide-react";
import { useOptionalEditorUIContext } from "@/context/EditorUIContext";
import { useOptionalTemplateContext } from "@/context/TemplateContext";
import { QRCodeCanvas } from "qrcode.react";

interface FormFieldComponentProps {
  field: FormFieldType;
  template: Template;
  formState: Record<string, any>;
  isSelected: boolean;
  isPrimarySelection: boolean;
  isPartOfSelection: boolean;
  isHovered: boolean;
  isInteractive: boolean;
  isCard: boolean;
  onDragStart: (e: any) => void;
  onDrag: (data: DraggableData) => void;
  onDragStop: (data: DraggableData) => void;
  onResizeStart: (e: any) => void;
  onResizeStop: (dir: any, ref: HTMLElement, delta: any, pos: any) => void;
  onClick: (e: React.MouseEvent) => void;
  onDoubleClick: (e: React.MouseEvent) => void;
  renderContextMenu: (children: React.ReactNode) => React.ReactNode;
  inlineEditingFieldId: string | null;
  isShiftPressed: boolean;
}

const FormFieldComponent: React.FC<FormFieldComponentProps> = ({
  field,
  template,
  formState,
  isSelected,
  isPrimarySelection,
  isPartOfSelection,
  isHovered,
  isInteractive,
  isCard,
  onDragStart,
  onDrag,
  onDragStop,
  onResizeStart,
  onResizeStop,
  onClick,
  onDoubleClick,
  renderContextMenu,
  inlineEditingFieldId,
  isShiftPressed,
}) => {
  const { primaryColor, textColor, accentColor, fontFamily } =
    template.designSettings || defaultDesignSettings;
  const uiCtx = useOptionalEditorUIContext();
  const templateCtx = useOptionalTemplateContext();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const setInlineEditingFieldId = uiCtx?.setInlineEditingFieldId;
  const updateField = templateCtx?.updateField;

  const isEditingInline = isInteractive && inlineEditingFieldId === field.id;

  useEffect(() => {
    if (isEditingInline && textareaRef.current) {
      textareaRef.current.focus();
      textareaRef.current.select();
    }
  }, [isEditingInline]);

  const handleInlineEditSave = () => {
    if (isEditingInline && updateField && textareaRef.current && setInlineEditingFieldId) {
      updateField(field.id, { defaultValue: textareaRef.current.value });
      setInlineEditingFieldId(null);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Escape') {
      if (setInlineEditingFieldId) setInlineEditingFieldId(null);
    }
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      e.currentTarget.blur();
    }
  };

  const value = formState[field.id];
  const textStyles: React.CSSProperties =
    field.type === "static-text"
      ? {
          fontSize: isCard ? (field.fontSize || 12) * 0.6 : field.fontSize || 12,
          fontWeight: field.fontWeight || "normal",
          fontStyle: field.fontStyle || "normal",
          textAlign: field.textAlign || "left",
          color: field.color || textColor,
          fontFamily: fontFamily,
        }
      : { fontFamily: fontFamily };
  const tableSummary =
    field.type === "table" ? calculateTableSummary(field, value || []) : [];

  const processPlaceholders = (text: string, allFields: FormFieldType[], currentFormState: Record<string, any>): string => {
    if (!text) return '';
    return text.replace(/\{\{([^}]+)\}\}/g, (match, fieldId) => {
      const trimmedFieldId = fieldId.trim();
      const val = currentFormState[trimmedFieldId];
      if (val !== undefined && val !== null && val !== '') {
        return String(val);
      }
      const f = allFields.find(f => f.id === trimmedFieldId);
      if (f?.defaultValue) {
        return String(f.defaultValue);
      }
      return `[${f?.label || trimmedFieldId}]`;
    });
  };

  const rndComponent = (
    <Rnd
      size={{ width: field.width, height: field.height }}
      position={{ x: field.x, y: field.y }}
      onDragStart={onDragStart}
      onDrag={(e, data) => onDrag(data)}
      onDragStop={(e, d) => onDragStop(d)}
      onResizeStart={onResizeStart}
      onResizeStop={(e, dir, ref, delta, pos) => onResizeStop(dir, ref, delta, pos)}
      bounds="parent"
      disableDragging={!isInteractive || field.isLocked || isPartOfSelection}
      enableResizing={isInteractive && !field.isLocked && !isPartOfSelection}
      lockAspectRatio={isShiftPressed || field.type === 'qrcode'}
      className={cn(
        "transition-colors z-10",
        isInteractive && "hover:border-blue-300 hover:bg-blue-50/50 border",
        isHovered && !isSelected && "bg-yellow-100/50 border-yellow-400",
        isSelected && {
          "border-red-500 bg-red-100/50 border-solid": field.isLocked, // Locked overrides all other selection styles
          "border-blue-500 bg-blue-100/50 border-solid": !field.isLocked && isPrimarySelection, // Primary selection (single or multi)
          "border-gray-400 bg-gray-100/50 border-solid": !field.isLocked && !isPrimarySelection, // Part of multi-select but not primary
        },
        (field.type === "static-text" || field.type === "box" || field.type === "line" || field.type === "arrow") && !isInteractive && "border-none",
        field.type !== "static-text" && field.type !== "box" && field.type !== "line" && field.type !== "arrow" && { borderColor: accentColor }
      )}
      style={{
        borderColor: isSelected
          ? undefined
          : isInteractive && field.type !== "static-text" && field.type !== "box" && field.type !== "line" && field.type !== "arrow"
          ? accentColor
          : "transparent",
      }}
      onClick={onClick}
    >
      <div
        className={cn(
          "w-full h-full break-words whitespace-pre-wrap",
          field.type !== "box" && "p-1",
          isCard ? "text-[8px] leading-tight" : "text-sm"
        )}
        style={textStyles}
        onDoubleClick={onDoubleClick}
      >
        {field.type === "signature" && value ? (
          <img src={value} alt="Signature" className="h-full w-full object-contain" />
        ) : field.type === "image" && value ? (
          <img src={value} alt={field.label} className="h-full w-full object-cover" />
        ) : field.type === "qrcode" ? (
          <QRCodeCanvas
            value={processPlaceholders(field.defaultValue as string, template.pages.flatMap(p => p.fields), formState)}
            size={Math.min(field.width, field.height) - 2}
            bgColor={"transparent"}
            fgColor={textColor}
            level={"H"}
            className="p-1"
          />
        ) : field.type === "checkbox" ? (
          <div className="flex items-center gap-2 h-full">
            <div className="w-4 h-4 border-2 flex items-center justify-center" style={{ borderColor: textColor }}>
              {value && <Check className="w-3 h-3" style={{ color: textColor }} />}
            </div>
            <span>{field.placeholder || field.label}</span>
          </div>
        ) : field.type === "radio" ? (
          <div className="flex flex-col gap-1">
            {(field.options || []).map(option => (
              <div key={option} className="flex items-center gap-2">
                <div className="w-3 h-3 border rounded-full flex items-center justify-center" style={{ borderColor: textColor }}>
                  {value === option && <div className="w-1.5 h-1.5 rounded-full" style={{ backgroundColor: textColor }} />}
                </div>
                <span>{option}</span>
              </div>
            ))}
          </div>
        ) : field.type === "static-text" ? (
          isEditingInline ? null : processPlaceholders(field.defaultValue as string, template.pages.flatMap(p => p.fields), formState)
        ) : field.type === "box" ? (
          <div className="w-full h-full" style={{
            backgroundColor: field.color || 'transparent',
            border: `${field.borderWidth || 0}px solid ${field.borderColor || 'transparent'}`,
            borderRadius: `${field.borderRadius || 0}px`,
          }} />
        ) : field.type === "line" ? (
          <div className="w-full h-full" style={{
            backgroundColor: field.color || textColor,
          }} />
        ) : field.type === "arrow" ? (() => {
            const direction = field.arrowDirection || 'right';
            const isVertical = direction === 'up' || direction === 'down';
            const thickness = isVertical ? field.width : field.height;
            const headSize = field.arrowheadSize || 8;
            const color = field.color || textColor;

            const lineStyle: React.CSSProperties = { position: 'absolute', backgroundColor: color };
            const headStyle: React.CSSProperties = { position: 'absolute', width: 0, height: 0 };

            if (isVertical) {
                lineStyle.width = thickness;
                lineStyle.height = `calc(100% - ${headSize}px)`;
                lineStyle.left = '50%';
                lineStyle.transform = 'translateX(-50%)';
                
                headStyle.left = '50%';
                headStyle.transform = 'translateX(-50%)';
                headStyle.borderLeft = `${headSize / 2}px solid transparent`;
                headStyle.borderRight = `${headSize / 2}px solid transparent`;

                if (direction === 'up') {
                    lineStyle.bottom = 0;
                    headStyle.top = 0;
                    headStyle.borderBottom = `${headSize}px solid ${color}`;
                } else { // down
                    lineStyle.top = 0;
                    headStyle.bottom = 0;
                    headStyle.borderTop = `${headSize}px solid ${color}`;
                }
            } else { // horizontal
                lineStyle.height = thickness;
                lineStyle.width = `calc(100% - ${headSize}px)`;
                lineStyle.top = '50%';
                lineStyle.transform = 'translateY(-50%)';

                headStyle.top = '50%';
                headStyle.transform = 'translateY(-50%)';
                headStyle.borderTop = `${headSize / 2}px solid transparent`;
                headStyle.borderBottom = `${headSize / 2}px solid transparent`;

                if (direction === 'right') {
                    lineStyle.left = 0;
                    headStyle.right = 0;
                    headStyle.borderLeft = `${headSize}px solid ${color}`;
                } else { // left
                    lineStyle.right = 0;
                    headStyle.left = 0;
                    headStyle.borderRight = `${headSize}px solid ${color}`;
                }
            }

            return (
                <div className="w-full h-full relative">
                    <div style={lineStyle} />
                    <div style={headStyle} />
                </div>
            );
          })()
        : field.type === "table" ? (
          <div className="w-full h-full overflow-hidden flex flex-col">
            <table className="w-full text-left border-collapse">
              <thead>
                <tr>
                  {field.columns?.map((c) => (
                    <th key={c.id} className="p-1 border" style={{ backgroundColor: primaryColor, color: "white", borderColor: accentColor }}>
                      {c.header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {(value || []).map((row: any, rIdx: number) => (
                  <tr key={rIdx}>
                    {field.columns?.map((c) => (
                      <td key={c.id} className="p-1 border" style={{ borderColor: accentColor }}>
                        {row[c.id] || ""}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
            {tableSummary.length > 0 && (
              <div className="mt-auto ml-auto w-1/2 p-1">
                {tableSummary.map((item) => (
                  <div key={item.label} className="flex justify-between">
                    <span className="font-bold">{item.label}</span>
                    <span>{item.value}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : value ? (
          String(value)
        ) : (
          <span className="text-gray-400">{field.placeholder || field.label}</span>
        )}
      </div>
      {isEditingInline && (
        <textarea
          ref={textareaRef}
          defaultValue={field.defaultValue as string}
          onBlur={handleInlineEditSave}
          onKeyDown={handleKeyDown}
          onClick={(e) => e.stopPropagation()}
          className="absolute inset-0 w-full h-full p-1 border-none resize-none bg-white bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-blue-500 z-50"
          style={{
            ...textStyles,
            lineHeight: '1.2',
          }}
        />
      )}
    </Rnd>
  );

  return renderContextMenu(rndComponent);
};

const areEqual = (prevProps: FormFieldComponentProps, nextProps: FormFieldComponentProps) => {
  const prevFormValue = prevProps.formState[prevProps.field.id];
  const nextFormValue = nextProps.formState[nextProps.field.id];

  if (prevProps.inlineEditingFieldId !== nextProps.inlineEditingFieldId && (prevProps.field.id === prevProps.inlineEditingFieldId || nextProps.field.id === nextProps.inlineEditingFieldId)) {
    return false;
  }

  if (prevProps.field.type === 'table' || prevProps.field.type === 'qrcode') {
    if (JSON.stringify(prevFormValue) !== JSON.stringify(nextFormValue) || prevProps.field.defaultValue !== nextProps.field.defaultValue) {
      return false;
    }
  } else if (prevFormValue !== nextFormValue) {
    return false;
  }

  return (
    prevProps.field === nextProps.field &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.isPrimarySelection === nextProps.isPrimarySelection &&
    prevProps.isHovered === nextProps.isHovered &&
    prevProps.isInteractive === nextProps.isInteractive &&
    prevProps.template.designSettings === nextProps.template.designSettings &&
    prevProps.isShiftPressed === nextProps.isShiftPressed
  );
};

export default React.memo(FormFieldComponent, areEqual);