import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  Eye, 
  EyeOff, 
  Trash2, 
  Plus, 
  AlertTriangle, 
  CheckCircle,
  Download,
  Upload
} from 'lucide-react';
import { PlaceholderManager, PlaceholderMapping } from '@/utils/placeholderManager';
import { Form<PERSON>ield } from '@/data/templates';
import { cn } from '@/lib/utils';

interface PlaceholderEditorPanelProps {
  placeholderManager: PlaceholderManager;
  onMappingUpdate: (mappings: PlaceholderMapping[]) => void;
  onFieldAdd: (field: FormField) => void;
  className?: string;
}

export const PlaceholderEditorPanel: React.FC<PlaceholderEditorPanelProps> = ({
  placeholderManager,
  onMappingUpdate,
  onFieldAdd,
  className
}) => {
  const [mappings, setMappings] = useState<PlaceholderMapping[]>([]);
  const [selectedMapping, setSelectedMapping] = useState<string | null>(null);
  const [validationResult, setValidationResult] = useState<any>(null);
  const [bulkRenamePattern, setBulkRenamePattern] = useState('');
  const [bulkRenameReplacement, setBulkRenameReplacement] = useState('');

  const fieldTypes: { value: FormField['type']; label: string }[] = [
    { value: 'text', label: 'Text' },
    { value: 'textarea', label: 'Text Area' },
    { value: 'date', label: 'Date' },
    { value: 'number', label: 'Number' },
    { value: 'email', label: 'Email' },
    { value: 'select', label: 'Dropdown' },
    { value: 'signature', label: 'Signature' }
  ];

  useEffect(() => {
    refreshMappings();
  }, [placeholderManager]);

  const refreshMappings = () => {
    const activeMappings = placeholderManager.getActiveMappings();
    setMappings(activeMappings);
    
    const validation = placeholderManager.validateMappings();
    setValidationResult(validation);
    
    onMappingUpdate(activeMappings);
  };

  const handleMappingUpdate = (placeholderId: string, updates: Partial<PlaceholderMapping>) => {
    placeholderManager.updatePlaceholderMapping(placeholderId, updates);
    refreshMappings();
  };

  const handleMappingDelete = (placeholderId: string) => {
    placeholderManager.removePlaceholderMapping(placeholderId);
    if (selectedMapping === placeholderId) {
      setSelectedMapping(null);
    }
    refreshMappings();
  };

  const handleBulkRename = () => {
    if (bulkRenamePattern && bulkRenameReplacement) {
      const count = placeholderManager.bulkRename(bulkRenamePattern, bulkRenameReplacement);
      setBulkRenamePattern('');
      setBulkRenameReplacement('');
      refreshMappings();
      
      // Show success message
      console.log(`Updated ${count} field names`);
    }
  };

  const handleApplyToEditor = () => {
    const fields = placeholderManager.convertToTemplateFields();
    fields.forEach(field => onFieldAdd(field));
  };

  const handleExportMappings = () => {
    const data = placeholderManager.exportMappings();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `placeholder-mappings-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImportMappings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        if (placeholderManager.importMappings(data)) {
          refreshMappings();
          console.log('Mappings imported successfully');
        } else {
          console.error('Failed to import mappings');
        }
      } catch (error) {
        console.error('Invalid mapping file:', error);
      }
    };
    reader.readAsText(file);
    event.target.value = '';
  };

  const selectedMappingData = selectedMapping ? mappings.find(m => m.placeholderId === selectedMapping) : null;

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Placeholder Manager ({mappings.length})
          </CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleExportMappings}>
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
            <Button variant="outline" size="sm" asChild>
              <label>
                <Upload className="h-4 w-4 mr-1" />
                Import
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportMappings}
                  className="hidden"
                />
              </label>
            </Button>
          </div>
        </div>

        {/* Validation Status */}
        {validationResult && (
          <Alert variant={validationResult.isValid ? "default" : "destructive"}>
            {validationResult.isValid ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertTriangle className="h-4 w-4" />
            )}
            <AlertDescription>
              {validationResult.isValid 
                ? "All placeholder mappings are valid"
                : `${validationResult.errors.length} errors, ${validationResult.warnings.length} warnings`
              }
            </AlertDescription>
          </Alert>
        )}
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <Tabs defaultValue="mappings" className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="mappings">Mappings</TabsTrigger>
            <TabsTrigger value="bulk">Bulk Edit</TabsTrigger>
            <TabsTrigger value="validation">Validation</TabsTrigger>
          </TabsList>

          <TabsContent value="mappings" className="flex-1 overflow-hidden">
            <div className="h-full flex gap-4">
              {/* Mappings List */}
              <div className="flex-1">
                <ScrollArea className="h-full">
                  <div className="space-y-2">
                    {mappings.map((mapping) => (
                      <div
                        key={mapping.placeholderId}
                        className={cn(
                          "border rounded-lg p-3 cursor-pointer transition-colors",
                          selectedMapping === mapping.placeholderId 
                            ? "border-primary bg-primary/5" 
                            : "hover:bg-accent/50"
                        )}
                        onClick={() => setSelectedMapping(mapping.placeholderId)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline" className="text-xs">
                            {fieldTypes.find(t => t.value === mapping.fieldType)?.label}
                          </Badge>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleMappingUpdate(mapping.placeholderId, { 
                                  isActive: !mapping.isActive 
                                });
                              }}
                              className="h-6 w-6 p-0"
                            >
                              {mapping.isActive ? (
                                <Eye className="h-3 w-3" />
                              ) : (
                                <EyeOff className="h-3 w-3" />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleMappingDelete(mapping.placeholderId);
                              }}
                              className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="text-sm font-medium">{mapping.mappedName}</div>
                        <div className="text-xs text-muted-foreground truncate">
                          {mapping.originalText}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>

              {/* Selected Mapping Details */}
              {selectedMappingData && (
                <div className="w-80 border-l pl-4">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="mapped-name">Field Name</Label>
                      <Input
                        id="mapped-name"
                        value={selectedMappingData.mappedName}
                        onChange={(e) => handleMappingUpdate(selectedMapping!, { 
                          mappedName: e.target.value 
                        })}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="field-type">Field Type</Label>
                      <Select
                        value={selectedMappingData.fieldType}
                        onValueChange={(value: FormField['type']) => 
                          handleMappingUpdate(selectedMapping!, { fieldType: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {fieldTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {selectedMappingData.position && (
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="pos-x">X Position</Label>
                          <Input
                            id="pos-x"
                            type="number"
                            value={selectedMappingData.position.x}
                            onChange={(e) => handleMappingUpdate(selectedMapping!, {
                              position: {
                                ...selectedMappingData.position!,
                                x: parseInt(e.target.value) || 0
                              }
                            })}
                          />
                        </div>
                        <div>
                          <Label htmlFor="pos-y">Y Position</Label>
                          <Input
                            id="pos-y"
                            type="number"
                            value={selectedMappingData.position.y}
                            onChange={(e) => handleMappingUpdate(selectedMapping!, {
                              position: {
                                ...selectedMappingData.position!,
                                y: parseInt(e.target.value) || 0
                              }
                            })}
                          />
                        </div>
                      </div>
                    )}

                    <div className="text-xs text-muted-foreground bg-muted p-2 rounded">
                      <strong>Original:</strong> {selectedMappingData.originalText}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="bulk" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="bulk-pattern">Find Pattern</Label>
                <Input
                  id="bulk-pattern"
                  placeholder="e.g., field"
                  value={bulkRenamePattern}
                  onChange={(e) => setBulkRenamePattern(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="bulk-replacement">Replace With</Label>
                <Input
                  id="bulk-replacement"
                  placeholder="e.g., input"
                  value={bulkRenameReplacement}
                  onChange={(e) => setBulkRenameReplacement(e.target.value)}
                />
              </div>
              <Button onClick={handleBulkRename} className="w-full">
                Apply Bulk Rename
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="validation" className="space-y-4">
            {validationResult && (
              <div className="space-y-4">
                {validationResult.errors.length > 0 && (
                  <div>
                    <h4 className="font-medium text-destructive mb-2">Errors</h4>
                    <div className="space-y-1">
                      {validationResult.errors.map((error: string, index: number) => (
                        <div key={index} className="text-sm text-destructive bg-destructive/10 p-2 rounded">
                          {error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {validationResult.warnings.length > 0 && (
                  <div>
                    <h4 className="font-medium text-yellow-600 mb-2">Warnings</h4>
                    <div className="space-y-1">
                      {validationResult.warnings.map((warning: string, index: number) => (
                        <div key={index} className="text-sm text-yellow-600 bg-yellow-50 p-2 rounded">
                          {warning}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {validationResult.isValid && (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                    <p className="text-green-600">All mappings are valid!</p>
                  </div>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>

        <Separator className="my-4" />
        
        <div className="flex gap-2">
          <Button onClick={handleApplyToEditor} className="flex-1">
            <Plus className="h-4 w-4 mr-2" />
            Apply to Editor
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
