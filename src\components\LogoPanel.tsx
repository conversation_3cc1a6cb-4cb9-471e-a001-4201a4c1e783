import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useTemplateContext } from "@/context/TemplateContext";
import { Trash2, Upload } from "lucide-react";
import { showError } from "@/utils/toast";

export const LogoPanel = () => {
  const { logo, setLogo, updateLogo, removeLogo } = useTemplateContext();

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) { showError("File is too large. Please upload an image under 2MB."); return; }
      const reader = new FileReader();
      reader.onloadend = () => { setLogo({ src: reader.result as string, x: 20, y: 20, width: 150, height: 75 }); };
      reader.readAsDataURL(file);
    }
  };

  if (!logo) {
    return (
      <Card>
        <CardHeader><CardTitle>Add Your Logo</CardTitle><CardDescription>Upload your company logo to brand this document.</CardDescription></CardHeader>
        <CardContent>
          <Label htmlFor="logo-upload" className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-accent">
            <Upload className="w-8 h-8 text-muted-foreground" /><span className="mt-2 text-sm text-muted-foreground">Click to upload (PNG, JPG)</span>
          </Label>
          <Input id="logo-upload" type="file" className="sr-only" accept="image/png, image/jpeg" onChange={handleLogoUpload} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader><CardTitle>Logo Settings</CardTitle><CardDescription>Adjust the position and size of your logo.</CardDescription></CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-center"><img src={logo.src} alt="Logo Preview" className="max-h-24 border p-2 rounded-md bg-white" /></div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2"><Label htmlFor="logo-x">X Position</Label><Input id="logo-x" type="number" value={logo.x} onChange={(e) => updateLogo({ x: parseInt(e.target.value, 10) || 0 })} /></div>
          <div className="space-y-2"><Label htmlFor="logo-y">Y Position</Label><Input id="logo-y" type="number" value={logo.y} onChange={(e) => updateLogo({ y: parseInt(e.target.value, 10) || 0 })} /></div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2"><Label htmlFor="logo-width">Width</Label><Input id="logo-width" type="number" value={logo.width} onChange={(e) => updateLogo({ width: parseInt(e.target.value, 10) || 0 })} /></div>
          <div className="space-y-2"><Label htmlFor="logo-height">Height</Label><Input id="logo-height" type="number" value={logo.height} onChange={(e) => updateLogo({ height: parseInt(e.target.value, 10) || 0 })} /></div>
        </div>
        <Button variant="destructive" className="w-full" onClick={removeLogo}><Trash2 className="mr-2 h-4 w-4" />Remove Logo</Button>
      </CardContent>
    </Card>
  );
};