import { FormField, VisibilityCondition } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

interface VisibilityPanelProps {
  field: FormField;
}

export const VisibilityPanel = ({ field }: VisibilityPanelProps) => {
  const { template, updateField } = useTemplateContext();

  const otherFields = template.pages.flatMap(p => p.fields).filter(
    (f) =>
      f.id !== field.id &&
      f.type !== "static-text" &&
      f.type !== "table" &&
      f.type !== "signature"
  );

  const handleConditionChange = (
    key: keyof VisibilityCondition,
    value: string
  ) =>
    updateField(field.id, {
      visibilityCondition: {
        ...(field.visibilityCondition || {
          fieldId: "",
          operator: "equals",
          value: "",
        }),
        [key]: value,
      } as VisibilityCondition,
    });

  const clearCondition = () =>
    updateField(field.id, { visibilityCondition: undefined });

  return (
    <AccordionItem value="visibility">
      <AccordionTrigger>Conditional Visibility</AccordionTrigger>
      <AccordionContent className="space-y-2 pt-4">
        <p className="text-sm text-muted-foreground">
          Show this field only when another field meets a condition.
        </p>
        {field.visibilityCondition ? (
          <div className="p-2 border rounded-md space-y-2 bg-muted/50">
            <div className="flex items-center gap-2">
              <span className="font-semibold">When</span>
              <Select
                value={field.visibilityCondition.fieldId}
                onValueChange={(v) => handleConditionChange("fieldId", v)}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select a field..." />
                </SelectTrigger>
                <SelectContent>
                  {otherFields.map((f) => (
                    <SelectItem key={f.id} value={f.id}>
                      {f.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Select
                value={field.visibilityCondition.operator}
                onValueChange={(v) => handleConditionChange("operator", v)}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equals">is equal to</SelectItem>
                  <SelectItem value="not_equals">is not equal to</SelectItem>
                  <SelectItem value="greater_than">is greater than</SelectItem>
                  <SelectItem value="less_than">is less than</SelectItem>
                  <SelectItem value="contains">contains</SelectItem>
                </SelectContent>
              </Select>
              <Input
                placeholder="Value"
                className="flex-1"
                value={field.visibilityCondition.value}
                onChange={(e) =>
                  handleConditionChange("value", e.target.value)
                }
              />
            </div>
            <Button
              variant="link"
              size="sm"
              className="text-red-500 p-0 h-auto"
              onClick={clearCondition}
            >
              Remove Condition
            </Button>
          </div>
        ) : (
          <Button
            variant="outline"
            className="w-full"
            onClick={() =>
              updateField(field.id, {
                visibilityCondition: {
                  fieldId: "",
                  operator: "equals",
                  value: "",
                },
              })
            }
          >
            Add Condition
          </Button>
        )}
      </AccordionContent>
    </AccordionItem>
  );
};