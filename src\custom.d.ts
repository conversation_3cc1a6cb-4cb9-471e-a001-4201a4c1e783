declare module 'react-signature-pad-wrapper' {
    import * as React from 'react';
    import OriginalSignaturePad from 'signature_pad';
  
    export interface SignaturePadProps extends React.HTMLAttributes<SignaturePad> {
      options?: OriginalSignaturePad.Options;
      canvasProps?: React.CanvasHTMLAttributes<HTMLCanvasElement>;
    }
  
    export default class SignaturePad extends React.Component<SignaturePadProps> {
      clear: () => void;
      isEmpty: () => boolean;
      toDataURL: (mimeType?: string, encoderOptions?: number) => string;
      fromDataURL: (base64String: string) => void;
      toData: () => OriginalSignaturePad.PointGroup[];
      fromData: (pointGroups: OriginalSignaturePad.PointGroup[]) => void;
      getSignaturePad: () => OriginalSignaturePad;
    }
}