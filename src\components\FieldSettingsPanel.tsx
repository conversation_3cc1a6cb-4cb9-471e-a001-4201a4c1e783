import { useTemplateContext } from "@/context/TemplateContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { MultiSelectPanel } from "./settings-panels";
import { SingleSelectPanel } from "./settings-panels/SingleSelectPanel";

export const FieldSettingsPanel = () => {
  const { template } = useTemplateContext();
  const { activeFieldIds } = useEditorUIContext();

  if (activeFieldIds.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        Select a field to see its settings.
      </div>
    );
  }

  if (activeFieldIds.length > 1) {
    return <MultiSelectPanel />;
  }

  const fieldId = activeFieldIds[0];
  const field = template.pages.flatMap(p => p.fields).find((f) => f.id === fieldId);

  if (!field) {
    return (
      <div className="p-4 text-center text-red-500">Field not found.</div>
    );
  }

  return <SingleSelectPanel field={field} />;
};