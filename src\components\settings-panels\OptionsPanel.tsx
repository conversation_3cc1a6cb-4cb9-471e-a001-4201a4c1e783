import { FormField } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { PlusCircle, Trash2 } from "lucide-react";

interface OptionsPanelProps {
  field: FormField;
}

export const OptionsPanel = ({ field }: OptionsPanelProps) => {
  const { updateField } = useTemplateContext();

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...(field.options || [])];
    newOptions[index] = value;
    updateField(field.id, { options: newOptions });
  };

  const addOption = () =>
    updateField(field.id, {
      options: [
        ...(field.options || []),
        `New Option ${(field.options?.length || 0) + 1}`,
      ],
    });

  const deleteOption = (index: number) =>
    updateField(field.id, {
      options: (field.options || []).filter((_, i) => i !== index),
    });

  return (
    <AccordionItem value="options">
      <AccordionTrigger>Options</AccordionTrigger>
      <AccordionContent className="space-y-2 pt-4">
        <Label>Options</Label>
        <div className="space-y-2">
          {(field.options || []).map((option, index) => (
            <div key={index} className="flex items-center gap-2">
              <Input
                value={option}
                onChange={(e) => handleOptionChange(index, e.target.value)}
              />
              <Button
                variant="ghost"
                size="icon"
                onClick={() => deleteOption(index)}
              >
                <Trash2 className="h-4 w-4 text-destructive" />
              </Button>
            </div>
          ))}
        </div>
        <Button
          variant="outline"
          size="sm"
          className="w-full mt-2"
          onClick={addOption}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Option
        </Button>
      </AccordionContent>
    </AccordionItem>
  );
};