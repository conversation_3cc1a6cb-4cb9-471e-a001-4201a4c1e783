import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  global: {
    headers: {
      'X-Client-Info': 'indian-railways-report-generator'
    }
  }
});

// Database types for TypeScript
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      templates: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          type: string;
          file_url: string | null;
          original_file_url: string | null;
          placeholder_map: any;
          placeholder_detection_method: string | null;
          processing_status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          type: string;
          file_url?: string | null;
          original_file_url?: string | null;
          placeholder_map?: any;
          placeholder_detection_method?: string | null;
          processing_status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          type?: string;
          file_url?: string | null;
          original_file_url?: string | null;
          placeholder_map?: any;
          placeholder_detection_method?: string | null;
          processing_status?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      reports: {
        Row: {
          id: string;
          user_id: string;
          template_id: string;
          data: any;
          generated_doc_url: string | null;
          generated_pdf_url: string | null;
          signature_url: string | null;
          status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          template_id: string;
          data?: any;
          generated_doc_url?: string | null;
          generated_pdf_url?: string | null;
          signature_url?: string | null;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          template_id?: string;
          data?: any;
          generated_doc_url?: string | null;
          generated_pdf_url?: string | null;
          signature_url?: string | null;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      signatures: {
        Row: {
          id: string;
          report_id: string;
          image_url: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          report_id: string;
          image_url: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          report_id?: string;
          image_url?: string;
          created_at?: string;
        };
      };
    };
  };
}
