import React from "react";
import { FormField } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface StylingPanelProps {
  field: FormField;
}

export const StylingPanel = ({ field }: StylingPanelProps) => {
  const { template, updateField } = useTemplateContext();
  const textAreaRef = React.useRef<HTMLTextAreaElement>(null);

  const otherFields = template.pages.flatMap(p => p.fields).filter(
    (f) =>
      f.id !== field.id &&
      f.type !== "static-text" &&
      f.type !== "table" &&
      f.type !== "signature"
  );

  const handleInsertPlaceholder = (fieldIdToInsert: string) => {
    const placeholder = `{{${fieldIdToInsert}}}`;
    const textarea = textAreaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;
    const newText = text.substring(0, start) + placeholder + text.substring(end);

    updateField(field.id, { defaultValue: newText });

    setTimeout(() => {
      textarea.focus();
      textarea.selectionStart = textarea.selectionEnd = start + placeholder.length;
    }, 0);
  };

  return (
    <AccordionItem value="styling">
      <AccordionTrigger>Styling</AccordionTrigger>
      <AccordionContent className="space-y-4 pt-4">
        <div className="space-y-2">
          <Label htmlFor="defaultValue">Text Content</Label>
          <Textarea
            id="defaultValue"
            ref={textAreaRef}
            value={(field.defaultValue as string) || ""}
            onChange={(e) =>
              updateField(field.id, { defaultValue: e.target.value })
            }
          />
          <p className="text-xs text-muted-foreground">
            Use the dropdown below to insert dynamic values from other fields.
          </p>
        </div>
        <div className="space-y-2">
          <Label htmlFor="insert-placeholder">Insert Field</Label>
          <Select onValueChange={handleInsertPlaceholder} value="">
            <SelectTrigger id="insert-placeholder">
              <SelectValue placeholder="Select a field to insert..." />
            </SelectTrigger>
            <SelectContent>
              {otherFields.map((f) => (
                <SelectItem key={f.id} value={f.id}>
                  {f.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="fontSize">Font Size</Label>
            <Input
              id="fontSize"
              type="number"
              value={field.fontSize || 12}
              onChange={(e) =>
                updateField(field.id, {
                  fontSize: parseInt(e.target.value, 10),
                })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="color">Color</Label>
            <Input
              id="color"
              type="color"
              value={field.color || "#000000"}
              onChange={(e) =>
                updateField(field.id, { color: e.target.value })
              }
            />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-2">
          <div className="space-y-2">
            <Label htmlFor="fontWeight">Weight</Label>
            <Select
              value={field.fontWeight || "normal"}
              onValueChange={(v) =>
                updateField(field.id, { fontWeight: v as any })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="bold">Bold</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="fontStyle">Style</Label>
            <Select
              value={field.fontStyle || "normal"}
              onValueChange={(v) =>
                updateField(field.id, { fontStyle: v as any })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="italic">Italic</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="textAlign">Align</Label>
            <Select
              value={field.textAlign || "left"}
              onValueChange={(v) =>
                updateField(field.id, { textAlign: v as any })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};