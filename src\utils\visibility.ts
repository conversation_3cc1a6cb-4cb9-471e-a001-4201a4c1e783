import { FormField, VisibilityCondition } from "@/data/templates";

export const isFieldVisible = (
  field: FormField,
  formState: Record<string, any>
): boolean => {
  if (!field.visibilityCondition) {
    return true;
  }

  const { fieldId, operator, value: conditionValue } = field.visibilityCondition;
  const targetFieldValue = formState[fieldId];

  if (targetFieldValue === undefined || targetFieldValue === null || targetFieldValue === '') {
    return false;
  }

  const targetStr = String(targetFieldValue).toLowerCase();
  const conditionStr = String(conditionValue).toLowerCase();
  
  const targetNum = parseFloat(targetStr);
  const conditionNum = parseFloat(conditionStr);

  switch (operator) {
    case 'equals':
      return targetStr === conditionStr;
    case 'not_equals':
      return targetStr !== conditionStr;
    case 'contains':
      return targetStr.includes(conditionStr);
    case 'greater_than':
      if (!isNaN(targetNum) && !isNaN(conditionNum)) {
        return targetNum > conditionNum;
      }
      return false;
    case 'less_than':
      if (!isNaN(targetNum) && !isNaN(conditionNum)) {
        return targetNum < conditionNum;
      }
      return false;
    default:
      return true;
  }
};