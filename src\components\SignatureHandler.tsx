import React from "react";
import { useFormContext } from "@/context/FormContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { SignatureDialog } from "@/components/SignatureDialog";

export const SignatureHandler = () => {
  const { handleInputChange } = useFormContext();
  const { isSignatureDialogOpen, closeSignatureDialog, activeSignatureFieldId } = useEditorUIContext();

  const handleSaveSignature = (dataUrl: string) => {
    if (activeSignatureFieldId) {
      handleInputChange(activeSignatureFieldId, dataUrl);
    }
    closeSignatureDialog();
  };

  return (
    <SignatureDialog
      isOpen={isSignatureDialogOpen}
      onClose={closeSignatureDialog}
      onSave={handleSaveSignature}
    />
  );
};