{"name": "dazzling-owl-scurry", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "latest", "@supabase/supabase-js": "^2.57.0", "@tanstack/react-query": "latest", "@types/react-rnd": "^8.0.0", "@types/recharts": "^2.0.1", "class-variance-authority": "latest", "clsx": "latest", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "docx-templates": "^4.14.1", "embla-carousel-react": "^8.6.0", "file-type": "^21.0.0", "input-otp": "^1.4.2", "jspdf": "^2.5.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.368.0", "mammoth": "^1.10.0", "next-themes": "^0.4.6", "pdf-parse": "^1.1.1", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.5", "react-rnd": "^10.5.2", "react-router-dom": "^6.22.3", "react-signature-canvas": "^1.0.6", "react-signature-pad-wrapper": "^4.1.1", "recharts": "^3.1.2", "sonner": "^2.0.7", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"@testing-library/jest-dom": "latest", "@testing-library/react": "latest", "@testing-library/user-event": "latest", "@types/node": "latest", "@types/pdf-parse": "^1.1.5", "@types/react": "latest", "@types/react-dom": "latest", "@types/react-signature-canvas": "latest", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "@vitejs/plugin-react": "latest", "@vitejs/plugin-react-swc": "latest", "@vitest/ui": "latest", "autoprefixer": "latest", "eslint": "latest", "eslint-plugin-react-hooks": "latest", "eslint-plugin-react-refresh": "latest", "jsdom": "latest", "postcss": "latest", "tailwindcss": "latest", "typescript": "latest", "vite": "latest", "vitest": "latest"}}