# Template Upload System - User Guide

## Overview

The Template Upload System allows you to upload existing DOCX or PDF documents and automatically convert them into editable form templates. The system uses intelligent placeholder detection to identify form fields and creates a fully functional template that integrates seamlessly with the Indian Railways Report Generator.

## Features

### 🔍 Intelligent Placeholder Detection
- **Explicit Placeholders**: Detects placeholders wrapped in double curly braces (e.g., `{{Name}}`, `{{Date}}`)
- **Auto-Detection**: Identifies form fields, blank lines with underscores, and repeated spaces
- **Context-Aware**: Uses surrounding text to suggest meaningful field names and types
- **Railway-Specific**: Recognizes railway terminology and creates appropriate field mappings

### 🛡️ Security & Validation
- **File Type Validation**: Only accepts DOCX and PDF files
- **Size Limits**: Maximum file size of 10MB
- **Content Scanning**: Scans for malicious content and suspicious patterns
- **File Integrity**: Validates file structure and headers

### 🎨 Auto-Generation
- **Smart Field Types**: Automatically determines field types (text, date, email, signature, etc.)
- **Layout Preservation**: Maintains original document structure and positioning
- **Field Grouping**: Groups related fields together for better organization
- **Validation Rules**: Generates appropriate validation rules based on field types

## How to Use

### Step 1: Upload Your Document

1. Click the **"Upload Template"** card on the main dashboard
2. In the upload dialog, either:
   - **Drag and drop** your DOCX or PDF file into the upload area
   - **Click to browse** and select your file from your computer

### Step 2: Configure Template Settings

After successful upload and processing, you'll see:

- **Template Name**: Edit the auto-generated name
- **Category**: Choose from Reports, Forms, Invoices, Contracts, or Custom
- **Subcategory**: Select a more specific category if available
- **Detection Results**: Summary of placeholders found

### Step 3: Review and Edit Placeholders

The system displays all detected placeholders with:

- **Field Name**: Editable name for the form field
- **Field Type**: Dropdown to change the field type
- **Confidence Score**: How confident the system is about the detection
- **Detection Method**: How the placeholder was found
- **Context**: Surrounding text that helped with detection

#### Available Field Types:
- **Text**: Single-line text input
- **Text Area**: Multi-line text input
- **Date**: Date picker
- **Number**: Numeric input with validation
- **Email**: Email input with validation
- **Dropdown**: Select from predefined options
- **Signature**: Digital signature field

### Step 4: Bulk Operations

Use the bulk editing features to:
- **Rename Multiple Fields**: Find and replace patterns in field names
- **Hide/Show Fields**: Toggle field visibility
- **Delete Unwanted Fields**: Remove incorrectly detected placeholders

### Step 5: Create Template

Click **"Create Template"** to finalize your template. The system will:
- Generate form fields with proper positioning
- Apply validation rules
- Save the template to your library
- Redirect you to the editor for further customization

## Supported File Formats

### DOCX Files
- Microsoft Word documents (.docx)
- OpenDocument Text (.odt) - limited support
- Rich Text Format (.rtf) - limited support

### PDF Files
- Standard PDF documents
- Text-based PDFs (not scanned images)
- Form-enabled PDFs

## Best Practices

### For Better Detection Results:

1. **Use Consistent Formatting**
   - Use the same style for similar fields
   - Maintain consistent spacing and alignment

2. **Clear Field Labels**
   - Place descriptive labels near input areas
   - Use standard terminology (Name, Date, Email, etc.)

3. **Explicit Placeholders**
   - Use `{{FieldName}}` format for guaranteed detection
   - Choose descriptive names: `{{EmployeeName}}` instead of `{{Name}}`

4. **Structured Layout**
   - Group related fields together
   - Use consistent spacing between sections

### Railway-Specific Templates:

The system recognizes common railway terms and creates appropriate fields:

- **Locomotive**: `{{LocoNumber}}`, `{{LocoType}}`
- **Station**: `{{StationName}}`, `{{StationCode}}`
- **Division**: `{{DivisionName}}`, `{{DivisionCode}}`
- **Personnel**: `{{DriverName}}`, `{{GuardName}}`, `{{SupervisorName}}`
- **Technical**: `{{TrainNumber}}`, `{{KmS}}`, `{{TrackSection}}`

## Troubleshooting

### Common Issues:

**File Upload Fails**
- Check file size (must be under 10MB)
- Ensure file format is DOCX or PDF
- Try saving the file in a different format

**No Placeholders Detected**
- Add explicit placeholders using `{{FieldName}}` format
- Use underscores for blank fields: `Name: _______`
- Ensure text is selectable (not an image)

**Incorrect Field Types**
- Manually change field types in the configuration step
- Use descriptive field names that indicate the expected content
- Add context around fields (e.g., "Email Address: _____")

**Security Validation Errors**
- File may contain suspicious content
- Try creating a clean version of the document
- Remove any embedded scripts or macros

### Error Messages:

- **"File size exceeds limit"**: Reduce file size or split into multiple templates
- **"Invalid file type"**: Only DOCX and PDF files are supported
- **"Security validation failed"**: File contains potentially harmful content
- **"Processing failed"**: Document structure is corrupted or unsupported

## Advanced Features

### Custom Field Mapping

For advanced users, you can:
- Export placeholder mappings as JSON
- Edit mappings externally
- Import modified mappings back into the system

### Integration with Existing Templates

Uploaded templates can be:
- Combined with existing templates
- Used as starting points for custom designs
- Shared with other users (when authentication is enabled)

### API Integration

The template upload system provides programmatic access through:
- REST API endpoints for file upload
- Webhook notifications for processing completion
- Batch processing capabilities

## Security Considerations

### Data Privacy
- Uploaded files are processed locally when possible
- No file content is stored permanently during processing
- Generated templates contain only the structure, not original content

### File Safety
- All uploads are scanned for malicious content
- File type validation prevents executable uploads
- Content sanitization removes potentially harmful elements

### Access Control
- Templates are associated with user accounts
- Row-level security ensures data isolation
- Audit logs track all template operations

## Support

For additional help:
- Check the FAQ section
- Review example templates
- Contact support for complex document structures
- Submit feedback for feature requests

## Examples

See the `examples/` directory for sample templates:
- `joint-report-template.docx` - Railway joint inspection report
- `ta-form-template.pdf` - Travel allowance form
- `custom-contract-template.docx` - Generic contract template
