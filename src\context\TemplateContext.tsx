import React, { createContext, useContext, ReactNode, useEffect, useState } from "react";
import { Template, FormField, DesignSettings, defaultDesignSettings, getAllTemplates, updateUserTemplate, saveUserTemplate, Logo, Watermark, Page, PageSetup, defaultPageSetup } from "@/data/templates";
import { useHistory } from "@/hooks/useHistory";
import { showError, showSuccess } from "@/utils/toast";
import { useNavigate } from "react-router-dom";
import { arrayMove } from "@dnd-kit/sortable";

interface DocumentState {
  template: Template;
  logo: Logo | null;
}

interface SaveOptions {
  title: string;
  category: string;
  subcategory?: string;
}

interface TemplateContextType {
  template: Template;
  initialTemplate: Template;
  logo: Logo | null;
  updateField: (id: string, newProps: Partial<FormField>) => void;
  updateMultipleFields: (ids: string[], newProps: Partial<FormField>) => void;
  bulkUpdateFields: (updates: (Partial<FormField> & { id: string })[]) => void;
  addField: (type: FormField["type"], label: string, additionalProps?: Partial<FormField>, pageId?: string) => string;
  addPage: () => void;
  deletePage: (pageId: string) => void;
  duplicatePage: (pageId: string) => void;
  moveFieldsToPage: (fieldIds: string[], targetPageId: string) => void;
  reorderPages: (activeId: string, overId: string) => void;
  updatePageSettings: (pageId: string, settings: Partial<Omit<Page, 'fields' | 'id'>>) => void;
  deleteFields: (ids: string[]) => void;
  duplicateFields: (ids: string[]) => string[];
  createGrid: (fieldIds: string[], config: { rows: number; cols: number; vSpacing: number; hSpacing: number }) => string[];
  resetTemplate: () => void;
  setLogo: (logo: Logo | null) => void;
  updateLogo: (newProps: Partial<Logo>) => void;
  removeLogo: () => void;
  saveAsTemplate: (options: SaveOptions, onComplete: () => void) => void;
  updateTemplate: () => void;
  updateDesignSettings: (newSettings: Partial<DesignSettings>) => void;
  updatePageSetup: (newSetup: Partial<PageSetup>) => void;
  updateWatermarkSettings: (newSettings: Partial<Watermark>) => void;
  alignFields: (alignment: 'top' | 'left' | 'right' | 'bottom' | 'h-center' | 'v-center', fieldIds: string[]) => void;
  distributeFields: (direction: 'horizontal' | 'vertical', fieldIds: string[]) => void;
  bringForward: (fieldId: string) => void;
  sendBackward: (fieldId: string) => void;
  bringToFront: (fieldId: string) => void;
  sendToBack: (fieldId: string) => void;
  groupFields: (fieldIds: string[]) => void;
  ungroupFields: (fieldIds: string[]) => void;
  nudgeFields: (ids: string[], dx: number, dy: number) => void;
  reorderFields: (activeId: string, overId: string) => void;
  toggleFieldVisibility: (id: string) => void;
  toggleFieldLock: (id: string) => void;
  setFieldsVisibility: (ids: string[], isVisible: boolean) => void;
  setFieldsLock: (ids: string[], isLocked: boolean) => void;
  copyFields: (ids: string[]) => void;
  pasteFields: () => string[];
  clipboard: FormField[] | null;
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

const TemplateContext = createContext<TemplateContextType | undefined>(undefined);

export const useTemplateContext = () => {
  const context = useContext(TemplateContext);
  if (!context) throw new Error("useTemplateContext must be used within a TemplateProvider");
  return context;
};

export const useOptionalTemplateContext = () => {
  return useContext(TemplateContext);
};

export const TemplateProvider = ({ children, template: initialTemplate }: { children: ReactNode; template: Template; }) => {
  const navigate = useNavigate();
  const [
    documentState,
    setDocumentState,
    undo,
    redo,
    canUndo,
    canRedo,
    resetDocumentHistory,
  ] = useHistory<DocumentState>({
    template: initialTemplate,
    logo: null,
  });
  const { template, logo } = documentState;
  const [clipboard, setClipboard] = useState<FormField[] | null>(null);

  const logoStorageKey = `logo-${initialTemplate.title}`;
  const templateStorageKey = `template-${initialTemplate.title}`;

  useEffect(() => {
    let restoredProgress = false;
    try {
      const savedTemplate = localStorage.getItem(templateStorageKey);
      const savedLogo = localStorage.getItem(logoStorageKey);
      if (savedTemplate || savedLogo) {
        const restoredTemplate = savedTemplate ? JSON.parse(savedTemplate) : initialTemplate;
        const restoredLogo = savedLogo ? JSON.parse(savedLogo) : null;
        
        restoredTemplate.designSettings = {
          ...defaultDesignSettings,
          ...restoredTemplate.designSettings,
        };

        resetDocumentHistory({ template: restoredTemplate, logo: restoredLogo });
        restoredProgress = true;
      }
      if (restoredProgress) {
        showSuccess("Your previous template edits have been restored.");
      }
    } catch (e) {
      console.error("Could not load template state:", e);
      showError("Could not restore your template edits.");
    }
  }, [templateStorageKey, logoStorageKey, initialTemplate, resetDocumentHistory]);

  useEffect(() => {
    try {
      localStorage.setItem(templateStorageKey, JSON.stringify(template));
      if (logo) {
        localStorage.setItem(logoStorageKey, JSON.stringify(logo));
      } else {
        localStorage.removeItem(logoStorageKey);
      }
    } catch (e) {
      console.error("Could not save template state:", e);
    }
  }, [template, logo, templateStorageKey, logoStorageKey]);

  const updateField = (id: string, newProps: Partial<FormField>) => {
    setDocumentState(prev => ({
      ...prev,
      template: {
        ...prev.template,
        pages: prev.template.pages.map(page => ({
          ...page,
          fields: page.fields.map(f => (f.id === id ? { ...f, ...newProps } : f)),
        })),
      },
    }));
  };

  const updateMultipleFields = (ids: string[], newProps: Partial<FormField>) => {
    setDocumentState(prev => ({
      ...prev,
      template: {
        ...prev.template,
        pages: prev.template.pages.map(page => ({
          ...page,
          fields: page.fields.map(f => (ids.includes(f.id) ? { ...f, ...newProps } : f)),
        })),
      },
    }));
  };

  const bulkUpdateFields = (updates: (Partial<FormField> & { id: string })[]) => {
    setDocumentState(prev => {
      const updatesMap = new Map(updates.map(u => [u.id, u]));
      return {
        ...prev,
        template: {
          ...prev.template,
          pages: prev.template.pages.map(page => ({
            ...page,
            fields: page.fields.map(f => (updatesMap.has(f.id) ? { ...f, ...updatesMap.get(f.id)! } : f)),
          })),
        },
      };
    });
  };

  const addPage = () => {
    setDocumentState(prev => {
      const newPage: Page = {
        id: `page_${Date.now()}`,
        fields: [],
      };
      return {
        ...prev,
        template: {
          ...prev.template,
          pages: [...prev.template.pages, newPage],
        },
      };
    });
    showSuccess("New page added!");
  };

  const deletePage = (pageId: string) => {
    setDocumentState(prev => {
      if (prev.template.pages.length <= 1) {
        showError("Cannot delete the last page.");
        return prev;
      }
      const newPages = prev.template.pages.filter(p => p.id !== pageId);
      showSuccess("Page deleted.");
      return {
        ...prev,
        template: {
          ...prev.template,
          pages: newPages,
        },
      };
    });
  };

  const duplicatePage = (pageId: string) => {
    setDocumentState(prev => {
      const pageToDuplicate = prev.template.pages.find(p => p.id === pageId);
      if (!pageToDuplicate) {
        showError("Page not found.");
        return prev;
      }

      const newPage: Page = {
        ...pageToDuplicate,
        id: `page_${Date.now()}`,
        fields: pageToDuplicate.fields.map(field => ({
          ...field,
          id: `${field.id.split('_')[0]}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
          groupId: undefined,
        })),
      };

      const pageIndex = prev.template.pages.findIndex(p => p.id === pageId);
      const newPages = [...prev.template.pages];
      newPages.splice(pageIndex + 1, 0, newPage);

      showSuccess("Page duplicated.");
      return {
        ...prev,
        template: {
          ...prev.template,
          pages: newPages,
        },
      };
    });
  };

  const reorderPages = (activeId: string, overId: string) => {
    setDocumentState(prev => {
      const oldIndex = prev.template.pages.findIndex(p => p.id === activeId);
      const newIndex = prev.template.pages.findIndex(p => p.id === overId);
      if (oldIndex === -1 || newIndex === -1) return prev;

      const reorderedPages = arrayMove(prev.template.pages, oldIndex, newIndex);
      
      return {
        ...prev,
        template: {
          ...prev.template,
          pages: reorderedPages,
        },
      };
    });
  };

  const updatePageSettings = (pageId: string, settings: Partial<Omit<Page, 'fields' | 'id'>>) => {
    setDocumentState(prev => ({
      ...prev,
      template: {
        ...prev.template,
        pages: prev.template.pages.map(p => 
          p.id === pageId ? { ...p, ...settings } : p
        ),
      },
    }));
  };

  const addField = (type: FormField["type"], label: string, additionalProps: Partial<FormField> = {}, pageId?: string) => {
    const newId = `${label.toLowerCase().replace(/[^a-z0-9]/g, "").slice(0, 10)}_${Date.now()}`;
    const newField: FormField = {
      id: newId, label, type, x: 20, y: 20, width: 150, height: 30,
      placeholder: `Enter ${label}`, ...additionalProps,
    };
    if (type === "select" && !newField.options) newField.options = ["Option 1", "Option 2"];
    if (type === "radio") { newField.height = 80; newField.width = 150; if (!newField.options) newField.options = ["Option A", "Option B"]; }
    if (type === "static-text") {
      newField.placeholder = "";
      if (!newField.defaultValue) newField.defaultValue = "Your text here";
      if (!newField.fontSize) newField.fontSize = 12;
    }
    if (type === "table" && !newField.columns) {
      newField.width = 532; newField.height = 300;
      newField.columns = [{ id: "description", header: "Description" }];
    }
    if (type === "textarea") { newField.height = 80; newField.width = 250; }
    if (type === "signature") { newField.height = 60; newField.width = 200; }
    if (type === "image") { newField.height = 150; newField.width = 150; }
    if (type === "checkbox") { newField.height = 24; newField.width = 150; newField.placeholder = "Accept terms"; }
    if (type === "box") {
      newField.width = 100;
      newField.height = 100;
      newField.color = "transparent";
      newField.borderColor = "#374151";
      newField.borderWidth = 1;
    }
    if (type === "line") {
      newField.width = 150;
      newField.height = 2;
      newField.color = "#374151";
    }
    if (type === "arrow") {
      newField.width = 150;
      newField.height = 2;
      newField.color = "#374151";
      newField.arrowDirection = 'right';
      newField.arrowheadSize = 8;
    }
    
    setDocumentState(prev => {
      const targetPageId = pageId || prev.template.pages[prev.template.pages.length - 1]?.id;
      if (!targetPageId) {
        showError("Could not find a page to add the field to.");
        return prev;
      }
      const updatedPages = prev.template.pages.map(page => {
        if (page.id === targetPageId) {
          return { ...page, fields: [...page.fields, newField] };
        }
        return page;
      });
      return { ...prev, template: { ...prev.template, pages: updatedPages } };
    });
    showSuccess(`Field "${label}" added!`);
    return newField.id;
  };

  const moveFieldsToPage = (fieldIds: string[], targetPageId: string) => {
    setDocumentState(prev => {
        const allFields = prev.template.pages.flatMap(p => p.fields);
        const fieldsToMove = allFields.filter(f => fieldIds.includes(f.id));
        if (fieldsToMove.length === 0) return prev;

        const pagesWithoutFields = prev.template.pages.map(page => ({
            ...page,
            fields: page.fields.filter(f => !fieldIds.includes(f.id))
        }));

        const finalPages = pagesWithoutFields.map(page => {
            if (page.id === targetPageId) {
                return {
                    ...page,
                    fields: [...page.fields, ...fieldsToMove]
                };
            }
            return page;
        });

        showSuccess(`${fieldsToMove.length} field(s) moved.`);
        return {
            ...prev,
            template: {
                ...prev.template,
                pages: finalPages
            }
        };
    });
  };

  const deleteFields = (ids: string[]) => {
    setDocumentState(prev => ({
      ...prev,
      template: {
        ...prev.template,
        pages: prev.template.pages.map(page => ({
          ...page,
          fields: page.fields.filter(f => !ids.includes(f.id)),
        })),
      },
    }));
    showSuccess(`${ids.length} field(s) deleted.`);
  };

  const duplicateFields = (ids: string[]) => {
    const newIds: string[] = [];
    const allFields = template.pages.flatMap(p => p.fields);
    const allCurrentLabels = new Set(allFields.map(f => f.label));

    setDocumentState(prev => {
      const updatedPages = prev.template.pages.map(page => {
        const fieldsToDuplicate = page.fields.filter(f => ids.includes(f.id));
        if (fieldsToDuplicate.length === 0) return page;

        const duplicatedOnThisPage: FormField[] = [];
        fieldsToDuplicate.forEach(field => {
          const newId = `${field.id.split('_')[0]}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
          const baseLabel = field.label.replace(/ \(Copy( \d+)?\)$/, "");
          let copyNumber = 1;
          let newLabel = `${baseLabel} (Copy)`;
          while (allCurrentLabels.has(newLabel)) {
            copyNumber++;
            newLabel = `${baseLabel} (Copy ${copyNumber})`;
          }
          allCurrentLabels.add(newLabel);

          const newField: FormField = {
            ...field,
            id: newId,
            x: field.x + 10,
            y: field.y + 10,
            label: newLabel,
            groupId: undefined,
          };
          duplicatedOnThisPage.push(newField);
          newIds.push(newId);
        });
        return { ...page, fields: [...page.fields, ...duplicatedOnThisPage] };
      });
      return { ...prev, template: { ...prev.template, pages: updatedPages } };
    });

    showSuccess(`${ids.length} field(s) duplicated!`);
    return newIds;
  };

  const createGrid = (fieldIds: string[], config: { rows: number; cols: number; vSpacing: number; hSpacing: number }) => {
    const newIds: string[] = [];
    const allFields = template.pages.flatMap(p => p.fields);
    const selection = allFields.filter(f => fieldIds.includes(f.id));
    if (selection.length === 0) return [];

    const pageId = template.pages.find(p => p.fields.some(f => f.id === fieldIds[0]))?.id;
    if (!pageId) return [];

    const minX = Math.min(...selection.map(f => f.x));
    const minY = Math.min(...selection.map(f => f.y));
    const maxX = Math.max(...selection.map(f => f.x + f.width));
    const maxY = Math.max(...selection.map(f => f.y + f.height));
    const selectionWidth = maxX - minX;
    const selectionHeight = maxY - minY;

    const newFields: FormField[] = [];

    for (let row = 0; row < config.rows; row++) {
      for (let col = 0; col < config.cols; col++) {
        if (row === 0 && col === 0) continue;

        const dx = col * (selectionWidth + config.hSpacing);
        const dy = row * (selectionHeight + config.vSpacing);

        selection.forEach(field => {
          const newId = `${field.id.split('_')[0]}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
          const newField: FormField = {
            ...field,
            id: newId,
            x: field.x + dx,
            y: field.y + dy,
            label: `${field.label} ${row + 1}-${col + 1}`,
            groupId: undefined,
          };
          newFields.push(newField);
          newIds.push(newId);
        });
      }
    }

    setDocumentState(prev => ({
      ...prev,
      template: {
        ...prev.template,
        pages: prev.template.pages.map(p => 
          p.id === pageId ? { ...p, fields: [...p.fields, ...newFields] } : p
        ),
      },
    }));

    showSuccess(`Created a ${config.rows}x${config.cols} grid.`);
    return newIds;
  };

  const copyFields = (ids: string[]) => {
    const allFields = template.pages.flatMap(p => p.fields);
    const fieldsToCopy = allFields.filter(f => ids.includes(f.id));
    if (fieldsToCopy.length > 0) {
      setClipboard(fieldsToCopy);
      showSuccess(`${fieldsToCopy.length} field(s) copied.`);
    }
  };

  const pasteFields = () => {
    if (!clipboard) return [];
    const newFields: FormField[] = [];
    const newIds: string[] = [];
    clipboard.forEach(field => {
      const newId = `${field.id.split('_')[0]}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
      const newField: FormField = { ...field, id: newId, x: field.x + 10, y: field.y + 10, groupId: undefined };
      newFields.push(newField);
      newIds.push(newId);
    });

    setDocumentState(prev => {
      const lastPageIndex = prev.template.pages.length - 1;
      if (lastPageIndex < 0) return prev;
      const updatedPages = prev.template.pages.map((page, index) => {
        if (index === lastPageIndex) {
          return { ...page, fields: [...page.fields, ...newFields] };
        }
        return page;
      });
      return { ...prev, template: { ...prev.template, pages: updatedPages } };
    });

    showSuccess(`${newFields.length} field(s) pasted!`);
    return newIds;
  };

  const resetTemplate = () => {
    resetDocumentHistory({ template: initialTemplate, logo: null });
    localStorage.removeItem(templateStorageKey);
    localStorage.removeItem(logoStorageKey);
    showSuccess("Template has been reset to its default state.");
  };

  const setLogo = (newLogo: Logo | null) => setDocumentState(prev => ({ ...prev, logo: newLogo }));
  const updateLogo = (newProps: Partial<Logo>) => setDocumentState(prev => (prev.logo ? { ...prev, logo: { ...prev.logo, ...newProps } } : prev));
  const removeLogo = () => { setDocumentState(prev => ({ ...prev, logo: null })); showSuccess("Logo removed."); };

  const updateDesignSettings = (newSettings: Partial<DesignSettings>) => {
    setDocumentState(prev => ({
      ...prev, template: { ...prev.template, designSettings: { ...(prev.template.designSettings || defaultDesignSettings), ...newSettings } },
    }));
  };

  const updatePageSetup = (newSetup: Partial<PageSetup>) => {
    setDocumentState(prev => ({
      ...prev,
      template: {
        ...prev.template,
        pageSetup: {
          ...(prev.template.pageSetup || defaultPageSetup),
          ...newSetup,
        },
      },
    }));
  };

  const updateWatermarkSettings = (newSettings: Partial<Watermark>) => {
    setDocumentState(prev => ({
      ...prev,
      template: {
        ...prev.template,
        watermark: {
          ...(prev.template.watermark || { text: "DRAFT", color: "#cccccc", opacity: 0.5, fontSize: 100, rotation: -45, enabled: false }),
          ...newSettings,
        },
      },
    }));
  };

  const alignFields = (alignment: 'top' | 'left' | 'right' | 'bottom' | 'h-center' | 'v-center', fieldIds: string[]) => {
    if (fieldIds.length < 2) return;
    const allFields = template.pages.flatMap(p => p.fields);
    const selectedFields = allFields.filter(f => fieldIds.includes(f.id));
    const anchorField = selectedFields.find(f => f.id === fieldIds[fieldIds.length - 1]);
    if (!anchorField) return;

    const updates = selectedFields.map(field => {
      if (field.id === anchorField.id) return { id: field.id };
      let newX = field.x, newY = field.y;
      switch (alignment) {
        case 'top': newY = anchorField.y; break;
        case 'bottom': newY = anchorField.y + anchorField.height - field.height; break;
        case 'left': newX = anchorField.x; break;
        case 'right': newX = anchorField.x + anchorField.width - field.width; break;
        case 'h-center': newX = anchorField.x + (anchorField.width / 2) - (field.width / 2); break;
        case 'v-center': newY = anchorField.y + (anchorField.height / 2) - (field.height / 2); break;
      }
      return { id: field.id, x: Math.round(newX), y: Math.round(newY) };
    });
    bulkUpdateFields(updates.filter(u => u.x !== undefined || u.y !== undefined) as any);
  };

  const distributeFields = (direction: 'horizontal' | 'vertical', fieldIds: string[]) => {
    if (fieldIds.length < 3) { showError("Select at least 3 fields to distribute."); return; }
    const allFields = template.pages.flatMap(p => p.fields);
    const selectedFields = allFields.filter(f => fieldIds.includes(f.id)).sort((a, b) => direction === 'horizontal' ? a.x - b.x : a.y - b.y);
    const first = selectedFields[0], last = selectedFields[selectedFields.length - 1], intermediates = selectedFields.slice(1, -1);
    let updates: (Partial<FormField> & { id: string })[] = [];

    if (direction === 'horizontal') {
      const totalWidth = selectedFields.reduce((s, f) => s + f.width, 0);
      const totalSpan = (last.x + last.width) - first.x;
      if (totalSpan < totalWidth) { showError("Not enough space to distribute horizontally."); return; }
      const gap = (totalSpan - totalWidth) / (selectedFields.length - 1);
      let currentX = first.x + first.width + gap;
      updates = intermediates.map(field => { const newField = { id: field.id, x: Math.round(currentX) }; currentX += field.width + gap; return newField; });
    } else {
      const totalHeight = selectedFields.reduce((s, f) => s + f.height, 0);
      const totalSpan = (last.y + last.height) - first.y;
      if (totalSpan < totalHeight) { showError("Not enough space to distribute vertically."); return; }
      const gap = (totalSpan - totalHeight) / (selectedFields.length - 1);
      let currentY = first.y + first.height + gap;
      updates = intermediates.map(field => { const newField = { id: field.id, y: Math.round(currentY) }; currentY += field.height + gap; return newField; });
    }
    bulkUpdateFields(updates);
  };

  const findPageAndFieldIndex = (pages: Page[], fieldId: string): [Page | null, number] => {
    for (const page of pages) {
      const index = page.fields.findIndex(f => f.id === fieldId);
      if (index !== -1) return [page, index];
    }
    return [null, -1];
  };

  const bringForward = (fieldId: string) => setDocumentState(prev => { const [page, i] = findPageAndFieldIndex(prev.template.pages, fieldId); if (!page || i === -1 || i === page.fields.length - 1) return prev; const f = [...page.fields]; [f[i], f[i + 1]] = [f[i + 1], f[i]]; const p = prev.template.pages.map(p => p.id === page.id ? { ...p, fields: f } : p); return { ...prev, template: { ...prev.template, pages: p } }; });
  const sendBackward = (fieldId: string) => setDocumentState(prev => { const [page, i] = findPageAndFieldIndex(prev.template.pages, fieldId); if (!page || i <= 0) return prev; const f = [...page.fields]; [f[i], f[i - 1]] = [f[i - 1], f[i]]; const p = prev.template.pages.map(p => p.id === page.id ? { ...p, fields: f } : p); return { ...prev, template: { ...prev.template, pages: p } }; });
  const bringToFront = (fieldId: string) => setDocumentState(prev => { const [page, i] = findPageAndFieldIndex(prev.template.pages, fieldId); if (!page || i === -1 || i === page.fields.length - 1) return prev; const f = [...page.fields]; const [item] = f.splice(i, 1); f.push(item); const p = prev.template.pages.map(p => p.id === page.id ? { ...p, fields: f } : p); return { ...prev, template: { ...prev.template, pages: p } }; });
  const sendToBack = (fieldId: string) => setDocumentState(prev => { const [page, i] = findPageAndFieldIndex(prev.template.pages, fieldId); if (!page || i <= 0) return prev; const f = [...page.fields]; const [item] = f.splice(i, 1); f.unshift(item); const p = prev.template.pages.map(p => p.id === page.id ? { ...p, fields: f } : p); return { ...prev, template: { ...prev.template, pages: p } }; });

  const groupFields = (fieldIds: string[]) => {
    if (fieldIds.length < 2) return;
    const groupId = `group_${Date.now()}`;
    updateMultipleFields(fieldIds, { groupId });
    showSuccess("Fields grouped.");
  };
  const ungroupFields = (fieldIds: string[]) => { if (fieldIds.length === 0) return; const firstField = template.pages.flatMap(p => p.fields).find(f => f.id === fieldIds[0]); if (!firstField?.groupId) return; const groupId = firstField.groupId; const idsToUngroup = template.pages.flatMap(p => p.fields).filter(f => f.groupId === groupId).map(f => f.id); updateMultipleFields(idsToUngroup, { groupId: undefined }); showSuccess("Fields ungrouped."); };
  const nudgeFields = (ids: string[], dx: number, dy: number) => setDocumentState(prev => ({ ...prev, template: { ...prev.template, pages: prev.template.pages.map(p => ({ ...p, fields: p.fields.map(f => ids.includes(f.id) ? { ...f, x: f.x + dx, y: f.y + dy } : f) })) } }));
  const reorderFields = (activeId: string, overId: string) => { if (activeId === overId) return; setDocumentState(prev => { const [activePage] = findPageAndFieldIndex(prev.template.pages, activeId); const [overPage] = findPageAndFieldIndex(prev.template.pages, overId); if (!activePage || !overPage || activePage.id !== overPage.id) return prev; const reversed = [...activePage.fields].reverse(), oldIdx = reversed.findIndex(f => f.id === activeId), newIdx = reversed.findIndex(f => f.id === overId); if (oldIdx === -1 || newIdx === -1) return prev; const reorderedFields = arrayMove(reversed, oldIdx, newIdx).reverse(); const newPages = prev.template.pages.map(p => p.id === activePage.id ? { ...p, fields: reorderedFields } : p); return { ...prev, template: { ...prev.template, pages: newPages } }; }); };

  const toggleFieldVisibility = (id: string) => updateField(id, { isVisible: !(template.pages.flatMap(p => p.fields).find(f => f.id === id)?.isVisible ?? true) });
  const toggleFieldLock = (id: string) => updateField(id, { isLocked: !(template.pages.flatMap(p => p.fields).find(f => f.id === id)?.isLocked ?? false) });
  const setFieldsVisibility = (ids: string[], isVisible: boolean) => updateMultipleFields(ids, { isVisible });
  const setFieldsLock = (ids: string[], isLocked: boolean) => updateMultipleFields(ids, { isLocked });

  const saveAsTemplate = (options: SaveOptions, onComplete: () => void) => {
    try {
      if (getAllTemplates().some(t => t.title === options.title)) {
        showError("A template with this title already exists.");
        return;
      }
      const newTemplate: Template = {
        ...template,
        title: options.title,
        category: options.category,
        subcategory: options.subcategory,
        description: `Custom template based on "${template.title}".`,
        isCustom: true,
      };
      saveUserTemplate(newTemplate);
      showSuccess(`Template "${options.title}" saved!`);
      onComplete();
      navigate("/");
    } catch (e) {
      console.error("Failed to save template:", e);
      showError("Error saving template.");
    }
  };

  const updateTemplate = () => {
    if (!initialTemplate.isCustom) { showError("Cannot update a default template. Use 'Save as' first."); return; }
    try {
      updateUserTemplate(initialTemplate.title, template);
      showSuccess(`Template "${initialTemplate.title}" updated!`);
      resetDocumentHistory({ template, logo });
    } catch (e) { console.error("Failed to update template:", e); showError("Error updating template."); }
  };

  const value: TemplateContextType = {
    template, initialTemplate, logo, updateField, updateMultipleFields, bulkUpdateFields, addField, addPage, deletePage, duplicatePage, moveFieldsToPage, reorderPages, updatePageSettings, deleteFields, duplicateFields, createGrid, resetTemplate,
    setLogo, updateLogo, removeLogo, saveAsTemplate, updateTemplate, updateDesignSettings, updatePageSetup, updateWatermarkSettings, alignFields,
    distributeFields, bringForward, sendBackward, bringToFront, sendToBack, groupFields, ungroupFields,
    nudgeFields, reorderFields, toggleFieldVisibility, toggleFieldLock, setFieldsVisibility, setFieldsLock,
    copyFields, pasteFields, clipboard,
    undo, redo, canUndo, canRedo,
  };

  return (
    <TemplateContext.Provider value={value}>
      {children}
    </TemplateContext.Provider>
  );
};