import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "./ui/card";

interface PanelProps {
  children: React.ReactNode;
}

export const Panel: React.FC<PanelProps> = ({ children }) => {
  return <Card className="border-none shadow-none rounded-none">{children}</Card>;
};

interface PanelHeaderProps {
  title: string;
  subtitle?: string;
}

export const PanelHeader: React.FC<PanelHeaderProps> = ({ title, subtitle }) => {
  return (
    <CardHeader>
      <CardTitle>{title}</CardTitle>
      {subtitle && <CardDescription>{subtitle}</CardDescription>}
    </CardHeader>
  );
};

interface PanelContentProps {
  children: React.ReactNode;
}

export const PanelContent: React.FC<PanelContentProps> = ({ children }) => {
  return <CardContent>{children}</CardContent>;
};