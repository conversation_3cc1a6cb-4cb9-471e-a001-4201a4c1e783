import React, { useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Upload } from "lucide-react";
import { showError } from "@/utils/toast";

interface ImportTemplateCardProps {
  onImport: (file: File) => void;
}

export const ImportTemplateCard: React.FC<ImportTemplateCardProps> = ({ onImport }) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    inputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.type !== "application/json") {
        showError("Please select a valid JSON file.");
        return;
      }
      onImport(file);
      // Reset input value to allow importing the same file again
      e.target.value = "";
    }
  };

  return (
    <Card
      className="overflow-hidden h-full flex flex-col group cursor-pointer transition-all duration-300 hover:shadow-xl border-2 border-dashed hover:border-primary"
      onClick={handleClick}
    >
      <CardContent className="p-4 flex-grow flex flex-col items-center justify-center text-center">
        <Upload className="h-12 w-12 text-muted-foreground group-hover:text-primary transition-colors" />
        <h3 className="font-semibold mt-4">Import Template</h3>
        <p className="text-sm text-muted-foreground mt-1">
          Click to upload a .json file.
        </p>
        <input
          type="file"
          ref={inputRef}
          className="hidden"
          accept=".json,application/json"
          onChange={handleFileChange}
        />
      </CardContent>
    </Card>
  );
};