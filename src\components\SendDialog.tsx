import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { showSuccess, showError, showLoading, dismissToast } from "@/utils/toast";
import { useTemplateContext } from "@/context/TemplateContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { useFormContext } from "@/context/FormContext";
import { generatePdfDocument } from "@/utils/pdfGenerator";

export const SendDialog: React.FC = () => {
  const { template, logo } = useTemplateContext();
  const { formState, runFullValidation } = useFormContext();
  const { isSendDialogOpen, setIsSendDialogOpen } = useEditorUIContext();
  const [isLoading, setIsLoading] = useState(false);

  const handleSend = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!runFullValidation()) {
      showError("Please fix form errors before sending.");
      return;
    }

    setIsLoading(true);
    const toastId = showLoading("Generating and sending document...");

    try {
      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const doc = await generatePdfDocument(template, formState, logo);
      const pdfBlob = doc.output("blob");

      console.log("Simulating sending PDF. Blob data:", pdfBlob);

      dismissToast(toastId);
      showSuccess(`Document "${template.title}" sent successfully!`);
      setIsSendDialogOpen(false);
    } catch (error) {
      console.error("Failed to send document:", error);
      dismissToast(toastId);
      showError("Failed to generate or send the document.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isSendDialogOpen} onOpenChange={setIsSendDialogOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Send "{template.title}"</DialogTitle>
          <DialogDescription>
            Enter the recipient's email address to send this document.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSend}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                defaultValue="<EMAIL>"
                className="col-span-3"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Sending..." : "Send Document"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};