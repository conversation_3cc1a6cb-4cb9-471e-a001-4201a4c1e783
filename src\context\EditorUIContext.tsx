import React, { createContext, useContext, ReactNode, useState, useCallback, useEffect } from "react";
import { SignatureHandler } from "@/components/SignatureHandler";
import { useTemplateContext } from "./TemplateContext";

interface EditorUIContextType {
  activeFieldIds: string[];
  setActiveFieldIds: React.Dispatch<React.SetStateAction<string[]>>;
  handleFieldSelect: (fieldId: string | null, isMultiSelect: boolean, forceSingle?: boolean) => void;
  zoomLevel: number;
  setZoomLevel: (zoom: number) => void;
  horizontalGuides: number[];
  verticalGuides: number[];
  addGuide: (orientation: 'horizontal' | 'vertical', position: number) => void;
  updateGuide: (orientation: 'horizontal' | 'vertical', index: number, position: number) => void;
  removeGuide: (orientation: 'horizontal' | 'vertical', index: number) => void;
  isPreviewMode: boolean;
  togglePreviewMode: () => void;
  isSignatureDialogOpen: boolean;
  activeSignatureFieldId: string | null;
  openSignatureDialog: (fieldId: string) => void;
  closeSignatureDialog: () => void;
  isSendDialogOpen: boolean;
  setIsSendDialogOpen: (isOpen: boolean) => void;
  isSaveDialogOpen: boolean;
  setIsSaveDialogOpen: (isOpen: boolean) => void;
  hoveredFieldId: string | null;
  setHoveredFieldId: React.Dispatch<React.SetStateAction<string | null>>;
  inlineEditingFieldId: string | null;
  setInlineEditingFieldId: React.Dispatch<React.SetStateAction<string | null>>;
  activePageId: string | null;
  setActivePageId: React.Dispatch<React.SetStateAction<string | null>>;
  isShiftPressed: boolean;
}

const EditorUIContext = createContext<EditorUIContextType | undefined>(undefined);

export const useEditorUIContext = () => {
  const context = useContext(EditorUIContext);
  if (!context) throw new Error("useEditorUIContext must be used within an EditorUIProvider");
  return context;
};

export const useOptionalEditorUIContext = () => {
  return useContext(EditorUIContext);
};

export const EditorUIProvider = ({ children }: { children: ReactNode }) => {
  const { template } = useTemplateContext();
  const [activeFieldIds, setActiveFieldIds] = useState<string[]>([]);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isPreviewMode, setPreviewMode] = useState(false);
  const [horizontalGuides, setHorizontalGuides] = useState<number[]>([]);
  const [verticalGuides, setVerticalGuides] = useState<number[]>([]);
  const [isSignatureDialogOpen, setSignatureDialogOpen] = useState(false);
  const [activeSignatureFieldId, setActiveSignatureFieldId] = useState<string | null>(null);
  const [isSendDialogOpen, setIsSendDialogOpen] = useState(false);
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [hoveredFieldId, setHoveredFieldId] = useState<string | null>(null);
  const [inlineEditingFieldId, setInlineEditingFieldId] = useState<string | null>(null);
  const [activePageId, setActivePageId] = useState<string | null>(template.pages[0]?.id || null);
  const [isShiftPressed, setIsShiftPressed] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Shift') setIsShiftPressed(true);
    };
    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === 'Shift') setIsShiftPressed(false);
    };
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  const handleFieldSelect = useCallback((fieldId: string | null, isMultiSelect: boolean, forceSingle: boolean = false) => {
    if (fieldId === null) {
      setActiveFieldIds([]);
      return;
    }
  
    const clickedField = template.pages.flatMap(p => p.fields).find(f => f.id === fieldId);
  
    if (!isMultiSelect && clickedField?.groupId && !forceSingle) {
      const groupMemberIds = template.pages.flatMap(p => p.fields)
        .filter(f => f.groupId === clickedField.groupId)
        .map(f => f.id);
      setActiveFieldIds(groupMemberIds);
      return;
    }
  
    if (isMultiSelect) {
      setActiveFieldIds(prevIds => 
        prevIds.includes(fieldId) 
          ? prevIds.filter(id => id !== fieldId) 
          : [...prevIds, fieldId]
      );
    } else {
      setActiveFieldIds([fieldId]);
    }
  }, [template.pages]);

  const addGuide = (orientation: 'horizontal' | 'vertical', position: number) => {
    if (orientation === 'horizontal') {
      setHorizontalGuides(prev => [...prev, position]);
    } else {
      setVerticalGuides(prev => [...prev, position]);
    }
  };

  const updateGuide = (orientation: 'horizontal' | 'vertical', index: number, position: number) => {
    if (orientation === 'horizontal') {
      setHorizontalGuides(prev => {
        const newGuides = [...prev];
        newGuides[index] = position;
        return newGuides;
      });
    } else {
      setVerticalGuides(prev => {
        const newGuides = [...prev];
        newGuides[index] = position;
        return newGuides;
      });
    }
  };

  const removeGuide = (orientation: 'horizontal' | 'vertical', index: number) => {
    if (orientation === 'horizontal') {
      setHorizontalGuides(prev => prev.filter((_, i) => i !== index));
    } else {
      setVerticalGuides(prev => prev.filter((_, i) => i !== index));
    }
  };

  const togglePreviewMode = () => setPreviewMode(!isPreviewMode);

  const openSignatureDialog = (fieldId: string) => {
    setActiveSignatureFieldId(fieldId);
    setSignatureDialogOpen(true);
  };

  const closeSignatureDialog = () => {
    setSignatureDialogOpen(false);
    setActiveSignatureFieldId(null);
  };

  const value: EditorUIContextType = {
    activeFieldIds, setActiveFieldIds, handleFieldSelect, zoomLevel, setZoomLevel,
    horizontalGuides, verticalGuides, addGuide, updateGuide, removeGuide, isPreviewMode, togglePreviewMode,
    isSignatureDialogOpen, activeSignatureFieldId, openSignatureDialog, closeSignatureDialog,
    isSendDialogOpen, setIsSendDialogOpen, isSaveDialogOpen, setIsSaveDialogOpen,
    hoveredFieldId, setHoveredFieldId,
    inlineEditingFieldId, setInlineEditingFieldId,
    activePageId, setActivePageId,
    isShiftPressed,
  };

  return (
    <EditorUIContext.Provider value={value}>
      {children}
      <SignatureHandler />
    </EditorUIContext.Provider>
  );
};