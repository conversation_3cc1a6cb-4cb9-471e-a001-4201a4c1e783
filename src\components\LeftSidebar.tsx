import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { EditorForm } from "./EditorForm";
import { FieldsPanel } from "./FieldsPanel";
import { AddFieldPanel } from "./AddFieldPanel";
import { PagesPanel } from "./PagesPanel";

export const LeftSidebar = () => {
  return (
    <div className="h-full flex flex-col bg-gray-50">
      <Tabs defaultValue="fill" className="flex-grow flex flex-col">
        <TabsList className="grid w-full grid-cols-4 shrink-0">
          <TabsTrigger value="fill">Fill</TabsTrigger>
          <TabsTrigger value="layers">Layers</TabsTrigger>
          <TabsTrigger value="add">Add</TabsTrigger>
          <TabsTrigger value="pages">Pages</TabsTrigger>
        </TabsList>
        <div className="flex-grow overflow-y-auto">
          <TabsContent value="fill">
            <EditorForm />
          </TabsContent>
          <TabsContent value="layers">
            <FieldsPanel />
          </TabsContent>
          <TabsContent value="add">
            <AddFieldPanel />
          </TabsContent>
          <TabsContent value="pages">
            <PagesPanel />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};