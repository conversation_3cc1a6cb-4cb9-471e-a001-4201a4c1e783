import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useTemplateContext } from "@/context/TemplateContext";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PageFormat, PageOrientation } from "@/utils/pageUtils";
import { defaultPageSetup } from "@/data/templates";

export const PageSetupPanel = () => {
  const { template, updatePageSetup } = useTemplateContext();
  const pageSetup = template.pageSetup || defaultPageSetup;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Page Setup</CardTitle>
        <CardDescription>Adjust the size and orientation of your document pages.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="page-format">Page Format</Label>
          <Select
            value={pageSetup.format}
            onValueChange={(value) => updatePageSetup({ format: value as PageFormat })}
          >
            <SelectTrigger id="page-format">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="letter">Letter (8.5 x 11 in)</SelectItem>
              <SelectItem value="a4">A4 (210 x 297 mm)</SelectItem>
              <SelectItem value="legal">Legal (8.5 x 14 in)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="page-orientation">Orientation</Label>
          <Select
            value={pageSetup.orientation}
            onValueChange={(value) => updatePageSetup({ orientation: value as PageOrientation })}
          >
            <SelectTrigger id="page-orientation">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="portrait">Portrait</SelectItem>
              <SelectItem value="landscape">Landscape</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
};