import { Template } from '@/data/templates';
import { supabase } from '@/lib/supabase';

export interface StorageProvider {
  getAllTemplates(): Promise<Template[]>;
  saveTemplate(template: Template): Promise<void>;
  updateTemplate(originalTitle: string, updatedTemplate: Template): Promise<void>;
  deleteTemplate(title: string): Promise<void>;
  migrateFromLocalStorage(): Promise<void>;
}

/**
 * Local storage provider for backward compatibility
 */
class LocalStorageProvider implements StorageProvider {
  private readonly USER_TEMPLATES_KEY = "user-templates";

  async getAllTemplates(): Promise<Template[]> {
    try {
      const userTemplates: Template[] = JSON.parse(
        localStorage.getItem(this.USER_TEMPLATES_KEY) || "[]"
      );
      return userTemplates.map(t => ({ ...t, isCustom: true }));
    } catch (e) {
      console.error("Failed to load user templates from localStorage:", e);
      return [];
    }
  }

  async saveTemplate(template: Template): Promise<void> {
    try {
      const userTemplates = await this.getAllTemplates();
      userTemplates.push({ ...template, isCustom: true });
      localStorage.setItem(this.USER_TEMPLATES_KEY, JSON.stringify(userTemplates));
    } catch (error) {
      console.error('Failed to save template to localStorage:', error);
      throw new Error('Failed to save template. Storage may be full or unavailable.');
    }
  }

  async updateTemplate(originalTitle: string, updatedTemplate: Template): Promise<void> {
    try {
      const userTemplates = await this.getAllTemplates();
      const index = userTemplates.findIndex(t => t.title === originalTitle);
      if (index !== -1) {
        userTemplates[index] = { ...updatedTemplate, isCustom: true };
        localStorage.setItem(this.USER_TEMPLATES_KEY, JSON.stringify(userTemplates));
      } else {
        throw new Error("Template to update not found.");
      }
    } catch (error) {
      console.error('Failed to update template in localStorage:', error);
      if (error instanceof Error && error.message === "Template to update not found.") {
        throw error;
      }
      throw new Error('Failed to update template. Storage may be full or unavailable.');
    }
  }

  async deleteTemplate(title: string): Promise<void> {
    try {
      const userTemplates = await this.getAllTemplates();
      const filteredTemplates = userTemplates.filter(t => t.title !== title);
      localStorage.setItem(this.USER_TEMPLATES_KEY, JSON.stringify(filteredTemplates));
    } catch (error) {
      console.error('Failed to delete template from localStorage:', error);
      throw new Error('Failed to delete template.');
    }
  }

  async migrateFromLocalStorage(): Promise<void> {
    // No migration needed for localStorage provider
    return Promise.resolve();
  }
}

/**
 * Supabase storage provider
 */
class SupabaseStorageProvider implements StorageProvider {
  private isAvailable = false;

  constructor() {
    this.checkAvailability();
  }

  private async checkAvailability(): Promise<void> {
    try {
      const { data, error } = await supabase.auth.getSession();
      this.isAvailable = !error && !!data.session;
    } catch (error) {
      console.log('Supabase not available, falling back to localStorage');
      this.isAvailable = false;
    }
  }

  async getAllTemplates(): Promise<Template[]> {
    if (!this.isAvailable) {
      throw new Error('Supabase not available');
    }

    try {
      const { data: session } = await supabase.auth.getSession();
      if (!session.session?.user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .eq('user_id', session.session.user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return (data || []).map(this.mapSupabaseToTemplate);
    } catch (error) {
      console.error('Failed to load templates from Supabase:', error);
      throw new Error('Failed to load templates from cloud storage');
    }
  }

  async saveTemplate(template: Template): Promise<void> {
    if (!this.isAvailable) {
      throw new Error('Supabase not available');
    }

    try {
      const { data: session } = await supabase.auth.getSession();
      if (!session.session?.user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('templates')
        .insert({
          user_id: session.session.user.id,
          name: template.title,
          type: template.category.toLowerCase(),
          placeholder_map: {
            template: template,
            pages: template.pages,
            designSettings: template.designSettings,
            pageSetup: template.pageSetup,
            watermark: template.watermark
          },
          processing_status: 'completed'
        });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Failed to save template to Supabase:', error);
      throw new Error('Failed to save template to cloud storage');
    }
  }

  async updateTemplate(originalTitle: string, updatedTemplate: Template): Promise<void> {
    if (!this.isAvailable) {
      throw new Error('Supabase not available');
    }

    try {
      const { data: session } = await supabase.auth.getSession();
      if (!session.session?.user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('templates')
        .update({
          name: updatedTemplate.title,
          type: updatedTemplate.category.toLowerCase(),
          placeholder_map: {
            template: updatedTemplate,
            pages: updatedTemplate.pages,
            designSettings: updatedTemplate.designSettings,
            pageSetup: updatedTemplate.pageSetup,
            watermark: updatedTemplate.watermark
          },
          updated_at: new Date().toISOString()
        })
        .eq('user_id', session.session.user.id)
        .eq('name', originalTitle);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Failed to update template in Supabase:', error);
      throw new Error('Failed to update template in cloud storage');
    }
  }

  async deleteTemplate(title: string): Promise<void> {
    if (!this.isAvailable) {
      throw new Error('Supabase not available');
    }

    try {
      const { data: session } = await supabase.auth.getSession();
      if (!session.session?.user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('templates')
        .delete()
        .eq('user_id', session.session.user.id)
        .eq('name', title);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Failed to delete template from Supabase:', error);
      throw new Error('Failed to delete template from cloud storage');
    }
  }

  async migrateFromLocalStorage(): Promise<void> {
    if (!this.isAvailable) {
      return;
    }

    try {
      const localProvider = new LocalStorageProvider();
      const localTemplates = await localProvider.getAllTemplates();
      
      if (localTemplates.length === 0) {
        return;
      }

      console.log(`Migrating ${localTemplates.length} templates from localStorage to Supabase...`);

      for (const template of localTemplates) {
        try {
          await this.saveTemplate(template);
        } catch (error) {
          console.error(`Failed to migrate template "${template.title}":`, error);
        }
      }

      console.log('Migration completed successfully');
    } catch (error) {
      console.error('Failed to migrate templates:', error);
    }
  }

  private mapSupabaseToTemplate(data: any): Template {
    const placeholderMap = data.placeholder_map || {};
    
    return {
      title: data.name,
      description: placeholderMap.template?.description || 'Template from cloud storage',
      category: this.capitalizeFirst(data.type),
      subcategory: placeholderMap.template?.subcategory,
      pages: placeholderMap.pages || [{ id: `page_${Date.now()}`, fields: [] }],
      designSettings: placeholderMap.designSettings,
      pageSetup: placeholderMap.pageSetup,
      watermark: placeholderMap.watermark,
      isCustom: true
    };
  }

  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

/**
 * Hybrid storage manager that automatically falls back to localStorage
 */
export class HybridTemplateStorage implements StorageProvider {
  private supabaseProvider: SupabaseStorageProvider;
  private localProvider: LocalStorageProvider;
  private useSupabase: boolean = false;

  constructor() {
    this.supabaseProvider = new SupabaseStorageProvider();
    this.localProvider = new LocalStorageProvider();
    this.initializeProvider();
  }

  private async initializeProvider(): Promise<void> {
    try {
      // Try to use Supabase first
      await this.supabaseProvider.getAllTemplates();
      this.useSupabase = true;
      
      // Migrate from localStorage if needed
      await this.supabaseProvider.migrateFromLocalStorage();
    } catch (error) {
      console.log('Using localStorage for template storage');
      this.useSupabase = false;
    }
  }

  async getAllTemplates(): Promise<Template[]> {
    try {
      if (this.useSupabase) {
        return await this.supabaseProvider.getAllTemplates();
      }
    } catch (error) {
      console.log('Supabase failed, falling back to localStorage');
      this.useSupabase = false;
    }
    
    return await this.localProvider.getAllTemplates();
  }

  async saveTemplate(template: Template): Promise<void> {
    try {
      if (this.useSupabase) {
        await this.supabaseProvider.saveTemplate(template);
        return;
      }
    } catch (error) {
      console.log('Supabase failed, falling back to localStorage');
      this.useSupabase = false;
    }
    
    await this.localProvider.saveTemplate(template);
  }

  async updateTemplate(originalTitle: string, updatedTemplate: Template): Promise<void> {
    try {
      if (this.useSupabase) {
        await this.supabaseProvider.updateTemplate(originalTitle, updatedTemplate);
        return;
      }
    } catch (error) {
      console.log('Supabase failed, falling back to localStorage');
      this.useSupabase = false;
    }
    
    await this.localProvider.updateTemplate(originalTitle, updatedTemplate);
  }

  async deleteTemplate(title: string): Promise<void> {
    try {
      if (this.useSupabase) {
        await this.supabaseProvider.deleteTemplate(title);
        return;
      }
    } catch (error) {
      console.log('Supabase failed, falling back to localStorage');
      this.useSupabase = false;
    }
    
    await this.localProvider.deleteTemplate(title);
  }

  async migrateFromLocalStorage(): Promise<void> {
    if (this.useSupabase) {
      await this.supabaseProvider.migrateFromLocalStorage();
    }
  }

  isUsingSupabase(): boolean {
    return this.useSupabase;
  }
}

// Export singleton instance
export const templateStorage = new HybridTemplateStorage();
