import React, { createContext, useContext, ReactNode, useEffect, useState, useCallback, useMemo } from "react";
import { Template } from "@/data/templates";
import { showSuccess } from "@/utils/toast";
import { validateField } from "@/utils/validation";
import { isFieldVisible } from "@/utils/visibility";
import { useTemplateContext } from "./TemplateContext";
import { faker } from "@/utils/faker";
import { useHistory } from "@/hooks/useHistory";

// Helper for table calculations
const evaluateCalculation = (formula: string, row: Record<string, any>): number | null => {
  if (!formula) return null;
  const populatedFormula = formula.replace(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g, (match) => {
    const value = parseFloat(row[match]);
    return isNaN(value) ? '0' : value.toString();
  });
  try {
    const result = new Function(`return ${populatedFormula}`)();
    return (typeof result === 'number' && isFinite(result)) ? result : null;
  } catch (e) {
    console.error("Calculation error:", e);
    return null;
  }
};

const randomItem = <T,>(arr: T[]): T => arr[Math.floor(Math.random() * arr.length)];

interface FormContextType {
  formState: Record<string, any>;
  validationErrors: Record<string, string | null>;
  handleInputChange: (id: string, value: any) => void;
  handleClearForm: () => void;
  addTableRow: (fieldId: string) => void;
  deleteTableRow: (fieldId: string, rowIndex: number) => void;
  handleTableRowChange: (fieldId: string, rowIndex: number, columnId: string, value: string) => void;
  runFullValidation: () => boolean;
  handleFillWithSampleData: () => void;
  loadFormState: (newState: Record<string, any>) => void;
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

const FormContext = createContext<FormContextType | undefined>(undefined);

export const useFormContext = () => {
  const context = useContext(FormContext);
  if (!context) throw new Error("useFormContext must be used within a FormProvider");
  return context;
};

export const useOptionalFormContext = () => {
  return useContext(FormContext);
};

export const FormProvider = ({ children }: { children: ReactNode }) => {
  const { template, initialTemplate } = useTemplateContext();
  const allFields = useMemo(() => template.pages.flatMap(p => p.fields), [template.pages]);

  const getInitialFormState = useCallback((tpl: Template): Record<string, any> => {
    const initialState: Record<string, any> = {};
    const fields = tpl.pages.flatMap(p => p.fields);
    fields.forEach(field => {
      if (field.defaultValue !== undefined && field.defaultValue !== null && field.defaultValue !== '') {
        if (field.type === 'checkbox') {
          initialState[field.id] = !!field.defaultValue;
        } else {
          initialState[field.id] = field.defaultValue;
        }
      }
    });
    return initialState;
  }, []);

  const [
    formState,
    setFormState,
    undo,
    redo,
    canUndo,
    canRedo,
    resetFormHistory,
  ] = useHistory<Record<string, any>>({});
  
  const [validationErrors, setValidationErrors] = useState<Record<string, string | null>>({});
  
  const formStorageKey = `formState-${initialTemplate.title}`;

  useEffect(() => {
    try {
      const savedFormState = localStorage.getItem(formStorageKey);
      const initialState = savedFormState 
        ? JSON.parse(savedFormState) 
        : getInitialFormState(initialTemplate);
      
      resetFormHistory(initialState);

      if (savedFormState) {
        showSuccess("Your form progress has been restored.");
      }
    } catch (e) { console.error("Could not load form state:", e); }
  }, [formStorageKey, initialTemplate, getInitialFormState, resetFormHistory]);

  useEffect(() => {
    try {
      localStorage.setItem(formStorageKey, JSON.stringify(formState));
    } catch (e) { console.error("Could not save form state:", e); }
  }, [formState, formStorageKey]);

  const runFullValidation = useCallback(() => {
    const errors: Record<string, string | null> = {};
    let isValid = true;
    allFields.forEach(field => {
      if (isFieldVisible(field, formState)) {
        const errorMessage = validateField(field, formState[field.id]);
        if (errorMessage) {
          errors[field.id] = errorMessage;
          isValid = false;
        } else {
          errors[field.id] = null;
        }
      }
    });
    setValidationErrors(errors);
    return isValid;
  }, [allFields, formState]);

  useEffect(() => {
    runFullValidation();
  }, [allFields, formState, runFullValidation]);

  const handleInputChange = (id: string, value: any) => {
    setFormState((prev) => ({ ...prev, [id]: value }));
  };

  const handleClearForm = () => {
    setFormState(getInitialFormState(template));
    setValidationErrors({});
    showSuccess("Form cleared!");
  };

  const addTableRow = (fieldId: string) => {
    setFormState(prev => ({ ...prev, [fieldId]: [...(prev[fieldId] || []), {}] }));
  };

  const deleteTableRow = (fieldId: string, rowIndex: number) => {
    setFormState(prev => {
      const currentRows = prev[fieldId] || [];
      const newRows = currentRows.filter((_: any, index: number) => index !== rowIndex);
      return { ...prev, [fieldId]: newRows };
    });
  };

  const handleTableRowChange = (fieldId: string, rowIndex: number, columnId: string, value: string) => {
    const tableField = allFields.find(f => f.id === fieldId && f.type === 'table');
    if (!tableField || !tableField.columns) return;

    setFormState(prev => {
      const newRows = [...(prev[fieldId] || [])];
      const updatedRow = { ...newRows[rowIndex], [columnId]: value };

      tableField.columns?.forEach(col => {
        if (col.calculation) {
          const result = evaluateCalculation(col.calculation, updatedRow);
          if (result !== null) updatedRow[col.id] = result.toFixed(2);
        }
      });

      newRows[rowIndex] = updatedRow;
      return { ...prev, [fieldId]: newRows };
    });
  };

  const handleFillWithSampleData = () => {
    const sampleState: Record<string, any> = {};
    allFields.forEach(field => {
      if (!isFieldVisible(field, formState)) return;

      let value: any;
      const label = field.label.toLowerCase();

      switch (field.type) {
        case 'text':
          if (label.includes('name')) value = faker.name();
          else if (label.includes('company')) value = faker.companyName();
          else if (label.includes('city')) value = faker.address().split(', ')[1];
          else if (label.includes('address')) value = faker.address();
          else if (label.includes('position') || label.includes('title')) value = "Project Manager";
          else value = faker.sentence();
          break;
        case 'email':
          value = faker.email();
          break;
        case 'number':
          value = faker.number(field.validation?.minValue, field.validation?.maxValue);
          break;
        case 'date':
          value = faker.date();
          break;
        case 'textarea':
          value = faker.paragraph();
          break;
        case 'select':
          value = field.options ? randomItem(field.options) : undefined;
          break;
        case 'radio':
          value = field.options ? randomItem(field.options) : undefined;
          break;
        case 'checkbox':
          value = faker.boolean();
          break;
        case 'table':
          const numRows = faker.number(2, 4);
          const rows = [];
          for (let i = 0; i < numRows; i++) {
            const row: Record<string, any> = {};
            field.columns?.forEach(col => {
              if (!col.calculation) {
                const colId = col.id.toLowerCase();
                if (colId.includes('item') || colId.includes('description')) row[col.id] = faker.productName();
                else if (colId.includes('quantity')) row[col.id] = faker.number(1, 10);
                else if (colId.includes('price') || colId.includes('cost')) row[col.id] = faker.number(10, 200);
                else row[col.id] = faker.number(1, 100);
              }
            });
            field.columns?.forEach(col => {
              if (col.calculation) {
                const result = evaluateCalculation(col.calculation, row);
                if (result !== null) row[col.id] = result.toFixed(2);
              }
            });
            rows.push(row);
          }
          value = rows;
          break;
        default:
          break;
      }
      if (value !== undefined) {
        sampleState[field.id] = value;
      }
    });
    setFormState(prev => ({ ...prev, ...sampleState }));
    showSuccess("Form filled with sample data!");
  };

  const loadFormState = (newState: Record<string, any>) => {
    setFormState(newState);
    showSuccess("Form data loaded successfully!");
  };

  const value: FormContextType = {
    formState, validationErrors, handleInputChange, handleClearForm,
    addTableRow, deleteTableRow, handleTableRowChange, runFullValidation,
    handleFillWithSampleData, loadFormState,
    undo, redo, canUndo, canRedo,
  };

  return (
    <FormContext.Provider value={value}>
      {children}
    </FormContext.Provider>
  );
};