# Joint Report Template Example

This example demonstrates how to create a Joint Report template for Indian Railways using the Template Upload System.

## Template Structure

### Header Section
```
JOINT REPORT
Division: {{Division}}
Date: {{ReportDate}}
Report No: {{ReportNumber}}
```

### Locomotive Details
```
Locomotive Number: {{LocoNumber}}
Locomotive Type: {{LocoType}}
Home Shed: {{HomeShed}}
Current Location: {{CurrentLocation}}
```

### Personnel Information
```
Loco Pilot: {{LocoPilotName}}
Assistant Loco Pilot: {{AssistantLocoPilotName}}
Guard: {{GuardName}}
Station Master: {{StationMasterName}}
```

### Incident Details
```
Date of Incident: {{IncidentDate}}
Time of Incident: {{IncidentTime}}
Location: {{IncidentLocation}}
KmS: {{KilometerStone}}
Track Section: {{TrackSection}}
```

### Description
```
Nature of Incident: {{IncidentNature}}

Detailed Description:
{{IncidentDescription}}

Immediate Action Taken:
{{ImmediateAction}}

Cause of Incident:
{{IncidentCause}}
```

### Damage Assessment
```
Locomotive Damage: {{LocomotiveDamage}}
Track Damage: {{TrackDamage}}
Other Damage: {{OtherDamage}}
Estimated Cost: {{EstimatedCost}}
```

### Recommendations
```
Preventive Measures:
{{PreventiveMeasures}}

Further Action Required:
{{FurtherAction}}
```

### Signatures
```
Prepared by: {{PreparedByName}}
Designation: {{PreparedByDesignation}}
Signature: {{PreparedBySignature}}
Date: {{PreparedDate}}

Reviewed by: {{ReviewedByName}}
Designation: {{ReviewedByDesignation}}
Signature: {{ReviewedBySignature}}
Date: {{ReviewedDate}}

Approved by: {{ApprovedByName}}
Designation: {{ApprovedByDesignation}}
Signature: {{ApprovedBySignature}}
Date: {{ApprovedDate}}
```

## Expected Field Detection

When this template is uploaded, the system should detect:

### Text Fields (18)
- Division
- ReportNumber
- LocoNumber
- LocoType
- HomeShed
- CurrentLocation
- LocoPilotName
- AssistantLocoPilotName
- GuardName
- StationMasterName
- IncidentLocation
- KilometerStone
- TrackSection
- IncidentNature
- PreparedByName
- PreparedByDesignation
- ReviewedByName
- ReviewedByDesignation
- ApprovedByName
- ApprovedByDesignation

### Date Fields (5)
- ReportDate
- IncidentDate
- PreparedDate
- ReviewedDate
- ApprovedDate

### Time Fields (1)
- IncidentTime

### Number Fields (1)
- EstimatedCost

### Text Area Fields (6)
- IncidentDescription
- ImmediateAction
- IncidentCause
- LocomotiveDamage
- TrackDamage
- OtherDamage
- PreventiveMeasures
- FurtherAction

### Signature Fields (3)
- PreparedBySignature
- ReviewedBySignature
- ApprovedBySignature

## Usage Instructions

1. **Create the Document**: Create a Word document with the above structure
2. **Upload**: Use the Template Upload feature to upload the document
3. **Review Detection**: Check that all 34 fields are detected correctly
4. **Customize**: Adjust field types and names as needed
5. **Finalize**: Create the template and start using it

## Customization Options

### Field Grouping
The system should automatically group fields into:
- **Header Information**: Division, ReportDate, ReportNumber
- **Locomotive Details**: LocoNumber, LocoType, HomeShed, CurrentLocation
- **Personnel**: All name fields
- **Incident Information**: Date, time, location details
- **Descriptions**: All text area fields
- **Signatures**: All signature-related fields

### Validation Rules
Recommended validation rules:
- **Required Fields**: ReportDate, LocoNumber, IncidentDate, PreparedByName
- **Date Validation**: All date fields should validate date format
- **Number Validation**: EstimatedCost should accept only positive numbers
- **Text Length**: Description fields should have reasonable character limits

### Design Customization
- Use railway blue color scheme (#003366)
- Include Indian Railways logo
- Use official fonts (Arial or similar)
- Maintain professional formatting

## Sample Data

For testing purposes, use this sample data:

```json
{
  "Division": "Northern Railway",
  "ReportDate": "2024-01-15",
  "ReportNumber": "NR/2024/001",
  "LocoNumber": "12345",
  "LocoType": "WAG-7",
  "HomeShed": "Tughlakabad",
  "CurrentLocation": "New Delhi",
  "LocoPilotName": "Rajesh Kumar",
  "AssistantLocoPilotName": "Suresh Singh",
  "GuardName": "Amit Sharma",
  "StationMasterName": "Pradeep Gupta",
  "IncidentDate": "2024-01-14",
  "IncidentTime": "14:30",
  "IncidentLocation": "Between Delhi and Ghaziabad",
  "KilometerStone": "15/2",
  "TrackSection": "Up Main Line",
  "IncidentNature": "Signal Passing at Danger",
  "IncidentDescription": "The locomotive passed the signal at danger due to poor visibility caused by fog.",
  "ImmediateAction": "Train was immediately stopped and reported to control.",
  "IncidentCause": "Poor visibility due to dense fog conditions.",
  "LocomotiveDamage": "No damage to locomotive",
  "TrackDamage": "No track damage",
  "OtherDamage": "None",
  "EstimatedCost": "0",
  "PreventiveMeasures": "Enhanced fog safety protocols to be implemented.",
  "FurtherAction": "Refresher training for crew on fog operations.",
  "PreparedByName": "Inspector Mohan Lal",
  "PreparedByDesignation": "Safety Inspector",
  "PreparedDate": "2024-01-15",
  "ReviewedByName": "Sunil Verma",
  "ReviewedByDesignation": "Assistant Safety Officer",
  "ReviewedDate": "2024-01-16",
  "ApprovedByName": "Rakesh Agarwal",
  "ApprovedByDesignation": "Divisional Safety Officer",
  "ApprovedDate": "2024-01-17"
}
```

## Integration with Existing System

This template integrates with:
- **Report Management System**: Auto-generates report numbers
- **Personnel Database**: Validates staff names and designations
- **Locomotive Database**: Validates locomotive numbers and types
- **Location Database**: Validates station names and track sections
- **Signature System**: Captures digital signatures from authorized personnel

## Compliance

This template ensures compliance with:
- Indian Railways Safety Manual
- Accident Investigation Procedures
- Documentation Standards
- Digital Signature Requirements
