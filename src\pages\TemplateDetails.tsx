import { use<PERSON>ara<PERSON>, useNavigate, <PERSON> } from "react-router-dom";
import { getAllTemplates } from "@/data/templates";
import { PdfPreview } from "@/components/PdfPreview";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft } from "lucide-react";
import { TemplateCard } from "@/components/TemplateCard";
import { MadeWithDyad } from "@/components/made-with-dyad";
import { ThemeToggle } from "@/components/ThemeToggle";

const TemplateDetails = () => {
  const { templateTitle } = useParams<{ templateTitle: string }>();
  const navigate = useNavigate();
  const decodedTitle = templateTitle ? decodeURIComponent(templateTitle) : "";
  const allTemplates = getAllTemplates();
  const template = allTemplates.find((t) => t.title === decodedTitle);

  if (!template || !template.pages || template.pages.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <p className="text-2xl mb-4">Template not found!</p>
        <Link to="/" className="text-blue-500 hover:underline">
          <ArrowLeft className="inline-block mr-2" />
          Back to Home
        </Link>
      </div>
    );
  }

  const relatedTemplates = allTemplates
    .filter((t) => t.category === template.category && t.title !== template.title)
    .slice(0, 3);

  const handleUseTemplate = () => {
    navigate(`/editor/${encodeURIComponent(template.title)}`);
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 sm:p-8">
      <div className="container mx-auto">
        <div className="flex justify-between items-center mb-8">
          <Button variant="outline" size="sm" asChild>
            <Link to="/">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Templates
            </Link>
          </Button>
          <ThemeToggle />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
          {/* Left side: PDF Preview */}
          <div className="lg:col-span-3 bg-gray-100 dark:bg-gray-900/50 rounded-lg p-4 flex justify-center items-start">
            <div className="w-full max-w-[612px]">
              <PdfPreview template={template} page={template.pages[0]} pageIndex={0} formState={{}} />
            </div>
          </div>

          {/* Right side: Details */}
          <div className="lg:col-span-2 space-y-6">
            <div>
              <Badge>{template.category}</Badge>
              <h1 className="text-3xl font-bold mt-2">{template.title}</h1>
              <p className="text-muted-foreground mt-2">{template.description}</p>
            </div>
            <Button size="lg" className="w-full" onClick={handleUseTemplate}>
              Use Template
            </Button>
            <div className="space-y-4 pt-4 border-t">
                <h2 className="text-xl font-semibold">About this template</h2>
                <p className="text-sm text-muted-foreground">
                    This {template.title} is a versatile document designed for {template.category.toLowerCase()}. 
                    It helps you to efficiently create and customize professional-looking documents. 
                    Use our drag-and-drop builder to update the terms, add or remove fields, include your logo, and change other design elements in a few easy clicks.
                </p>
            </div>
          </div>
        </div>

        {relatedTemplates.length > 0 && (
            <div className="mt-16">
                <h2 className="text-2xl font-bold mb-6">Related Templates</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {relatedTemplates.map((relatedTemplate) => (
                        <TemplateCard
                            key={relatedTemplate.title}
                            template={relatedTemplate}
                        />
                    ))}
                </div>
            </div>
        )}
      </div>
      <MadeWithDyad />
    </div>
  );
};

export default TemplateDetails;