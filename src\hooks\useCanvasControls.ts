import React, { useEffect, useState, useRef } from "react";

const MIN_ZOOM = 0.2;
const MAX_ZOOM = 4;

export const useCanvasControls = (
  scrollContainerRef: React.RefObject<HTMLDivElement>,
  zoomLevel: number,
  setZoomLevel: (zoom: number) => void
) => {
  const [isPanning, setIsPanning] = useState(false);
  const lastMousePos = useRef({ x: 0, y: 0 });
  const zoomData = useRef<{ mouseX: number; mouseY: number; oldZoom: number } | null>(null);

  // Panning effect
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space' && !isPanning) {
        const target = e.target as HTMLElement;
        if (["INPUT", "TEXTAREA", "SELECT"].includes(target.tagName) || target.isContentEditable) return;
        e.preventDefault();
        setIsPanning(true);
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        setIsPanning(false);
      }
    };

    const handleMouseDown = (e: MouseEvent) => {
      if (isPanning && e.button === 0) { // Only pan with left click
        e.preventDefault();
        container.style.cursor = 'grabbing';
        lastMousePos.current = { x: e.clientX, y: e.clientY };
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);
      }
    };

    const handleMouseMove = (e: MouseEvent) => {
      const dx = e.clientX - lastMousePos.current.x;
      const dy = e.clientY - lastMousePos.current.y;
      container.scrollLeft -= dx;
      container.scrollTop -= dy;
      lastMousePos.current = { x: e.clientX, y: e.clientY };
    };

    const handleMouseUp = () => {
      if (isPanning) container.style.cursor = 'grab';
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    container.addEventListener('mousedown', handleMouseDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      container.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isPanning, scrollContainerRef]);

  // Update cursor based on panning state
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;
    if (isPanning) {
      container.style.cursor = 'grab';
    } else {
      container.style.cursor = 'default';
    }
  }, [isPanning, scrollContainerRef]);

  // Zooming effect
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleWheel = (e: WheelEvent) => {
      if (e.ctrlKey) {
        e.preventDefault();
        const rect = container.getBoundingClientRect();
        zoomData.current = {
            mouseX: e.clientX - rect.left,
            mouseY: e.clientY - rect.top,
            oldZoom: zoomLevel,
        };
        const delta = e.deltaY > 0 ? -0.1 : 0.1;
        const newZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, zoomLevel + delta));
        if (newZoom !== zoomLevel) {
          setZoomLevel(newZoom);
        }
      }
    };

    container.addEventListener('wheel', handleWheel, { passive: false });
    return () => container.removeEventListener('wheel', handleWheel);
  }, [scrollContainerRef, zoomLevel, setZoomLevel]);

  // Scroll adjustment effect after zoom
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container || !zoomData.current) return;

    const { mouseX, mouseY, oldZoom } = zoomData.current;
    
    const mousePointTo = {
        x: (mouseX + container.scrollLeft) / oldZoom,
        y: (mouseY + container.scrollTop) / oldZoom,
    };

    const newScrollLeft = mousePointTo.x * zoomLevel - mouseX;
    const newScrollTop = mousePointTo.y * zoomLevel - mouseY;

    container.scrollLeft = newScrollLeft;
    container.scrollTop = newScrollTop;

    zoomData.current = null; // Reset after use
  }, [zoomLevel, scrollContainerRef]);
};