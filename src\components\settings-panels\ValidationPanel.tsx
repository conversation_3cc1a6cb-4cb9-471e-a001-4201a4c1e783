import { FormField, ValidationRule } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface ValidationPanelProps {
  field: FormField;
}

export const ValidationPanel = ({ field }: ValidationPanelProps) => {
  const { updateField } = useTemplateContext();

  const handleValidationChange = (key: keyof ValidationRule, value: any) => {
    const newValidation: ValidationRule = {
      ...(field.validation || {}),
      [key]: value,
    };
    if (value === "" || value === false || value === undefined)
      delete newValidation[key];
    updateField(field.id, { validation: newValidation });
  };

  return (
    <AccordionItem value="validation">
      <AccordionTrigger>Validation Rules</AccordionTrigger>
      <AccordionContent className="space-y-4 pt-4">
        <div className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
          <div className="space-y-0.5">
            <Label>Required</Label>
            <p className="text-xs text-muted-foreground">
              This field must be filled out.
            </p>
          </div>
          <Switch
            checked={field.validation?.required || false}
            onCheckedChange={(c) => handleValidationChange("required", c)}
          />
        </div>
        {(field.type === "text" || field.type === "textarea") && (
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minLength">Min Length</Label>
              <Input
                id="minLength"
                type="number"
                placeholder="e.g., 5"
                value={field.validation?.minLength || ""}
                onChange={(e) =>
                  handleValidationChange(
                    "minLength",
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxLength">Max Length</Label>
              <Input
                id="maxLength"
                type="number"
                placeholder="e.g., 100"
                value={field.validation?.maxLength || ""}
                onChange={(e) =>
                  handleValidationChange(
                    "maxLength",
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
              />
            </div>
          </div>
        )}
        {field.type === "number" && (
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minValue">Min Value</Label>
              <Input
                id="minValue"
                type="number"
                placeholder="e.g., 0"
                value={field.validation?.minValue || ""}
                onChange={(e) =>
                  handleValidationChange(
                    "minValue",
                    e.target.value ? parseFloat(e.target.value) : undefined
                  )
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxValue">Max Value</Label>
              <Input
                id="maxValue"
                type="number"
                placeholder="e.g., 1000"
                value={field.validation?.maxValue || ""}
                onChange={(e) =>
                  handleValidationChange(
                    "maxValue",
                    e.target.value ? parseFloat(e.target.value) : undefined
                  )
                }
              />
            </div>
          </div>
        )}
      </AccordionContent>
    </AccordionItem>
  );
};