import { FormField } from "@/data/templates";
import { useTemplateContext } from "@/context/TemplateContext";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface LineStylingPanelProps {
  field: FormField;
}

export const LineStylingPanel = ({ field }: LineStylingPanelProps) => {
  const { updateField } = useTemplateContext();

  return (
    <AccordionItem value="styling">
      <AccordionTrigger>Styling</AccordionTrigger>
      <AccordionContent className="space-y-4 pt-4">
        <div className="space-y-2">
          <Label htmlFor="color">Line Color</Label>
          <Input
            id="color"
            type="color"
            value={field.color || "#000000"}
            onChange={(e) =>
              updateField(field.id, { color: e.target.value })
            }
          />
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};