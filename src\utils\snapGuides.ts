import { FormField } from "@/data/templates";

const SNAP_THRESHOLD = 5;

type SnapLine = {
  direction: "horizontal" | "vertical";
  position: number;
  start: number;
  end: number;
};

export type SnapResult = {
  guides: SnapLine[];
  delta: { x: number; y: number };
};

const getSnapPoints = (field: FormField) => ({
  v: [field.x, field.x + field.width / 2, field.x + field.width],
  h: [field.y, field.y + field.height / 2, field.y + field.height],
});

export const getSnapGuides = (
  draggedFields: FormField[],
  staticFields: FormField[],
  horizontalGuides: number[] = [],
  verticalGuides: number[] = [],
  pageDimensions: { width: number; height: number }
): SnapResult => {
  if (draggedFields.length === 0) {
    return { guides: [], delta: { x: 0, y: 0 } };
  }

  const draggedBounds = {
    minX: Math.min(...draggedFields.map((f) => f.x)),
    maxX: Math.max(...draggedFields.map((f) => f.x + f.width)),
    minY: Math.min(...draggedFields.map((f) => f.y)),
    maxY: Math.max(...draggedFields.map((f) => f.y + f.height)),
    midX: 0,
    midY: 0,
  };
  draggedBounds.midX = draggedBounds.minX + (draggedBounds.maxX - draggedBounds.minX) / 2;
  draggedBounds.midY = draggedBounds.minY + (draggedBounds.maxY - draggedBounds.minY) / 2;

  const draggedSnapPoints = {
    v: [draggedBounds.minX, draggedBounds.midX, draggedBounds.maxX],
    h: [draggedBounds.minY, draggedBounds.midY, draggedBounds.maxY],
  };

  const staticSnapPoints: { v: { pos: number; field: FormField }[]; h: { pos: number; field: FormField }[] } = { v: [], h: [] };

  const pageField = { id: 'page', x: 0, y: 0, width: pageDimensions.width, height: pageDimensions.height } as FormField;
  getSnapPoints(pageField).v.forEach(pos => staticSnapPoints.v.push({ pos, field: pageField }));
  getSnapPoints(pageField).h.forEach(pos => staticSnapPoints.h.push({ pos, field: pageField }));

  horizontalGuides.forEach(pos => staticSnapPoints.h.push({ pos, field: pageField }));
  verticalGuides.forEach(pos => staticSnapPoints.v.push({ pos, field: pageField }));

  staticFields.forEach((field) => {
    const points = getSnapPoints(field);
    points.v.forEach(pos => staticSnapPoints.v.push({ pos, field }));
    points.h.forEach(pos => staticSnapPoints.h.push({ pos, field }));
  });

  let bestDeltaX = 0, bestDeltaY = 0;
  let minDiffX = SNAP_THRESHOLD, minDiffY = SNAP_THRESHOLD;

  for (const dragPos of draggedSnapPoints.v) {
    for (const staticPoint of staticSnapPoints.v) {
      const diff = staticPoint.pos - dragPos;
      if (Math.abs(diff) < minDiffX) {
        minDiffX = Math.abs(diff);
        bestDeltaX = diff;
      }
    }
  }

  for (const dragPos of draggedSnapPoints.h) {
    for (const staticPoint of staticSnapPoints.h) {
      const diff = staticPoint.pos - dragPos;
      if (Math.abs(diff) < minDiffY) {
        minDiffY = Math.abs(diff);
        bestDeltaY = diff;
      }
    }
  }
  
  if (minDiffX >= SNAP_THRESHOLD) bestDeltaX = 0;
  if (minDiffY >= SNAP_THRESHOLD) bestDeltaY = 0;

  const activeGuides: SnapLine[] = [];
  const finalDraggedBounds = {
    ...draggedBounds,
    minX: draggedBounds.minX + bestDeltaX,
    maxX: draggedBounds.maxX + bestDeltaX,
    minY: draggedBounds.minY + bestDeltaY,
    maxY: draggedBounds.maxY + bestDeltaY,
  };
  const finalDraggedSnapPoints = {
    v: [finalDraggedBounds.minX, (finalDraggedBounds.minX + finalDraggedBounds.maxX) / 2, finalDraggedBounds.maxX],
    h: [finalDraggedBounds.minY, (finalDraggedBounds.minY + finalDraggedBounds.maxY) / 2, finalDraggedBounds.maxY],
  };

  for (const dragPos of finalDraggedSnapPoints.v) {
    for (const staticPoint of staticSnapPoints.v) {
      if (Math.abs(dragPos - staticPoint.pos) < 0.1) {
        const staticField = staticPoint.field;
        const start = Math.min(finalDraggedBounds.minY, staticField.y);
        const end = Math.max(finalDraggedBounds.maxY, staticField.y + staticField.height);
        activeGuides.push({ direction: "vertical", position: staticPoint.pos, start, end });
      }
    }
  }

  for (const dragPos of finalDraggedSnapPoints.h) {
    for (const staticPoint of staticSnapPoints.h) {
      if (Math.abs(dragPos - staticPoint.pos) < 0.1) {
        const staticField = staticPoint.field;
        const start = Math.min(finalDraggedBounds.minX, staticField.x);
        const end = Math.max(finalDraggedBounds.maxX, staticField.x + staticField.width);
        activeGuides.push({ direction: "horizontal", position: staticPoint.pos, start, end });
      }
    }
  }

  return { guides: activeGuides, delta: { x: bestDeltaX, y: bestDeltaY } };
};