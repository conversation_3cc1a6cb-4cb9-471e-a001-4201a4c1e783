import { useState, useMemo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { AnimatedGradientText } from "@/components/AnimatedGradientText";
import { CategorySidebar } from "@/components/CategorySidebar";
import { SearchBar } from "@/components/SearchBar";
import { TemplateCard } from "@/components/TemplateCard";
import { MadeWithDyad } from "@/components/made-with-dyad";
import { PaginationControls } from "@/components/PaginationControls";
import {
  getAllTemplates,
  deleteUserTemplate,
  updateUserTemplate,
  saveUserTemplate,
  Template,
} from "@/data/templates";
import { ThemeToggle } from "@/components/ThemeToggle";
import { showError, showSuccess } from "@/utils/toast";
import { EditTemplateDialog } from "@/components/EditTemplateDialog";
import { CreateTemplateDialog } from "@/components/CreateTemplateDialog";
import { CreateTemplateCard } from "@/components/CreateTemplateCard";
import { ImportTemplateCard } from "@/components/ImportTemplateCard";

const ITEMS_PER_PAGE = 5;

const Index = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(
    null
  );
  const [allTemplates, setAllTemplates] = useState<Template[]>(() =>
    getAllTemplates()
  );
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const navigate = useNavigate();

  const refreshTemplates = useCallback(() => {
    setAllTemplates(getAllTemplates());
  }, []);

  const handleDeleteTemplate = (title: string) => {
    deleteUserTemplate(title);
    refreshTemplates();
    showSuccess(`Template "${title}" deleted successfully.`);
  };

  const handleEditTemplate = (template: Template) => {
    setEditingTemplate(template);
  };

  const handleUpdateTemplateDetails = (
    originalTitle: string,
    updatedDetails: {
      title: string;
      category: string;
      subcategory?: string;
    }
  ) => {
    const existingTemplate = allTemplates.find(t => t.title === originalTitle);
    if (!existingTemplate) {
      showError("Could not find the template to update.");
      return;
    }

    if (updatedDetails.title !== originalTitle && allTemplates.some(t => t.title === updatedDetails.title)) {
      showError("A template with that title already exists.");
      return;
    }

    const updatedTemplate = {
      ...existingTemplate,
      ...updatedDetails,
    };

    try {
      updateUserTemplate(originalTitle, updatedTemplate);
      refreshTemplates();
      setEditingTemplate(null);
      showSuccess("Template details updated successfully.");
    } catch (error) {
      showError("Failed to update template.");
      console.error(error);
    }
  };

  const handleCreateTemplate = (details: {
    title: string;
    category: string;
    subcategory?: string;
  }) => {
    if (allTemplates.some(t => t.title === details.title)) {
      showError("A template with that title already exists.");
      return;
    }

    const newTemplate: Template = {
      ...details,
      description: "A new template created from scratch.",
      pages: [{ id: `page_${Date.now()}`, fields: [] }],
      isCustom: true,
    };

    try {
      saveUserTemplate(newTemplate);
      showSuccess(`Template "${details.title}" created!`);
      setIsCreateDialogOpen(false);
      navigate(`/editor/${encodeURIComponent(details.title)}`);
    } catch (error) {
      showError("Failed to create template.");
      console.error(error);
    }
  };

  const handleImportTemplate = (file: File) => {
    // Validate file type and size
    if (!file.name.endsWith('.json') && file.type !== 'application/json') {
      showError("Please select a valid JSON file.");
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      showError("File size too large. Please select a file smaller than 5MB.");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const result = e.target?.result;
        if (typeof result !== 'string') {
          showError("Failed to read file content.");
          return;
        }

        const importedTemplate = JSON.parse(result);

        // Validate template structure and security
        if (!importedTemplate.title || !importedTemplate.pages) {
          throw new Error("Invalid template file structure.");
        }

        // Additional security validation
        if (typeof importedTemplate.title !== 'string' || importedTemplate.title.length > 100) {
          throw new Error("Invalid template title.");
        }

        // Check for potential XSS in template data
        const templateStr = JSON.stringify(importedTemplate);
        if (templateStr.includes('<script') || templateStr.includes('javascript:') || templateStr.includes('data:text/html')) {
          throw new Error("Template contains potentially malicious content.");
        }

        if (allTemplates.some(t => t.title === importedTemplate.title)) {
          showError(`A template named "${importedTemplate.title}" already exists. Please rename it before importing.`);
          return;
        }

        const newTemplate: Template = {
          ...importedTemplate,
          isCustom: true,
        };

        saveUserTemplate(newTemplate);
        refreshTemplates();
        showSuccess(`Template "${newTemplate.title}" imported successfully!`);
      } catch (error) {
        console.error("Import error:", error);
        showError("Failed to import template. The file might be corrupted or invalid.");
      }
    };
    reader.readAsText(file);
  };

  const handleCategoryChange = (
    category: string,
    subcategory: string | null
  ) => {
    if (category === "All Templates") {
      setSelectedCategory(null);
      setSelectedSubcategory(null);
    } else {
      setSelectedCategory(category);
      setSelectedSubcategory(subcategory);
    }
    setCurrentPage(1);
  };

  const filteredTemplates = useMemo(() => {
    return allTemplates.filter((template) => {
      const matchesSearch = template.title
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

      let matchesCategory = true;
      if (selectedCategory) {
        matchesCategory = template.category === selectedCategory;
        if (selectedSubcategory) {
          matchesCategory =
            matchesCategory && template.subcategory === selectedSubcategory;
        }
      }

      return matchesSearch && matchesCategory;
    });
  }, [searchTerm, selectedCategory, selectedSubcategory, allTemplates]);

  const totalPages = Math.ceil((filteredTemplates.length + 2) / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  
  const itemsToShow = [
    <CreateTemplateCard onClick={() => setIsCreateDialogOpen(true)} />,
    <ImportTemplateCard onImport={handleImportTemplate} />,
    ...filteredTemplates.map((template) => (
      <TemplateCard
        key={template.title}
        template={template}
        onDelete={handleDeleteTemplate}
        onEdit={handleEditTemplate}
      />
    )),
  ];

  const currentItems = itemsToShow.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 sm:p-8">
      <div className="absolute top-4 right-4 sm:top-8 sm:right-8">
        <ThemeToggle />
      </div>
      <header className="text-center mb-8">
        <AnimatedGradientText className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight">
          PDF Templates
        </AnimatedGradientText>
        <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
          Browse our collection of professionally designed PDF templates.
          Customize and use them for your business needs in just a few clicks.
        </p>
      </header>

      <main className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <aside className="md:col-span-1">
            <div className="sticky top-8 space-y-4">
              <CategorySidebar
                selectedCategory={selectedCategory}
                selectedSubcategory={selectedSubcategory}
                onCategoryChange={handleCategoryChange}
              />
            </div>
          </aside>
          <section className="md:col-span-3 space-y-6">
            <div className="flex flex-col sm:flex-row gap-4 justify-between sm:items-center">
              <div className="flex-grow">
                <SearchBar
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1);
                  }}
                />
              </div>
              <p className="text-sm text-muted-foreground text-right shrink-0">
                Showing {filteredTemplates.length} of {allTemplates.length}{" "}
                templates
              </p>
            </div>
            {currentItems.length > 0 ? (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {currentItems}
                </div>
                <div className="flex justify-center pt-4">
                  <PaginationControls
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              </>
            ) : (
              <div className="text-center py-16">
                <h3 className="text-xl font-semibold">No Templates Found</h3>
                <p className="text-muted-foreground mt-2">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            )}
          </section>
        </div>
      </main>
      <MadeWithDyad />
      <EditTemplateDialog
        template={editingTemplate}
        onClose={() => setEditingTemplate(null)}
        onSave={handleUpdateTemplateDetails}
      />
      <CreateTemplateDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onCreate={handleCreateTemplate}
      />
    </div>
  );
};

export default Index;