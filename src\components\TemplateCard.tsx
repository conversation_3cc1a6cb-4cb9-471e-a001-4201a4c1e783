import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { Template } from "@/data/templates";
import { PdfPreview } from "./PdfPreview";
import { Badge } from "./ui/badge";
import { But<PERSON> } from "./ui/button";
import { Trash2, Pencil, Download } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { showError } from "@/utils/toast";

interface TemplateCardProps {
  template: Template;
  onDelete?: (title: string) => void;
  onEdit?: (template: Template) => void;
}

export const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  onDelete,
  onEdit,
}) => {
  const navigate = useNavigate();

  const handleCardClick = () => {
    navigate(`/template/${encodeURIComponent(template.title)}`);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(template.title);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(template);
  };

  const handleExport = (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const templateJson = JSON.stringify(template, null, 2);
      const blob = new Blob([templateJson], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${template.title.replace(/ /g, "_")}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Export failed:", error);
      showError("Failed to export template.");
    }
  };

  return (
    <Card
      className="overflow-hidden h-full flex flex-col group cursor-pointer transition-shadow duration-300 hover:shadow-xl border relative"
      onClick={handleCardClick}
    >
      {template.isCustom && (
        <div className="absolute top-2 right-2 z-10 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="outline"
            size="icon"
            className="h-7 w-7 bg-background"
            onClick={handleExport}
          >
            <Download className="h-4 w-4" />
          </Button>
          {onEdit && (
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7 bg-background"
              onClick={handleEdit}
            >
              <Pencil className="h-4 w-4" />
            </Button>
          )}
          {onDelete && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="destructive"
                  size="icon"
                  className="h-7 w-7"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent onClick={(e) => e.stopPropagation()}>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete "{template.title}"?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete this
                    template.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDelete}>
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      )}
      <div className="relative h-48 overflow-hidden bg-gray-100 dark:bg-gray-800">
        <div
          style={{
            transform: "scale(0.24)",
            transformOrigin: "top left",
          }}
          className="transition-transform duration-300 group-hover:scale-[0.25]"
        >
          {template.pages && template.pages.length > 0 && (
            <PdfPreview template={template} page={template.pages[0]} pageIndex={0} formState={{}} variant="card" />
          )}
        </div>
        <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <span className="text-white font-semibold bg-black/50 px-3 py-1 rounded-md">
            View Details
          </span>
        </div>
      </div>
      <CardContent className="p-4 flex-grow flex flex-col">
        <div className="flex-grow">
          <h3 className="font-semibold line-clamp-1">{template.title}</h3>
          <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
            {template.description}
          </p>
        </div>
        <div className="mt-4 flex items-center gap-2 flex-wrap">
          <Badge variant={template.isCustom ? "secondary" : "outline"}>
            {template.isCustom ? "Custom" : template.category}
          </Badge>
          {template.subcategory && (
            <Badge variant="outline">{template.subcategory}</Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};