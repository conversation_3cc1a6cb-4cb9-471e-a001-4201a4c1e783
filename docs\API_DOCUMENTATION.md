# Template Upload API Documentation

## Overview

The Template Upload API provides programmatic access to the intelligent document processing and template generation system. This API allows developers to integrate template upload functionality into their applications.

## Base URL

```
https://your-app-domain.com/api
```

## Authentication

All API endpoints require authentication using JWT tokens:

```http
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### 1. Upload Template Document

Upload a DOCX or PDF document for processing.

**Endpoint:** `POST /templates/upload`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` (required): The document file to upload
- `templateName` (optional): Custom name for the template
- `category` (optional): Template category
- `options` (optional): Processing options as JSON

**Request Example:**
```bash
curl -X POST \
  -H "Authorization: Bearer <token>" \
  -F "file=@document.docx" \
  -F "templateName=My Custom Template" \
  -F "category=Reports" \
  -F "options={\"preserveLayout\":true,\"autoPositioning\":true}" \
  https://your-app-domain.com/api/templates/upload
```

**Response:**
```json
{
  "success": true,
  "data": {
    "templateId": "uuid-here",
    "processingStatus": "completed",
    "detectionResult": {
      "totalDetected": 5,
      "detectionMethod": "mixed",
      "placeholders": [
        {
          "id": "ph1",
          "text": "{{Name}}",
          "suggestedName": "employeeName",
          "suggestedType": "text",
          "confidence": 0.95,
          "detectionMethod": "explicit"
        }
      ]
    },
    "securityValidation": {
      "isValid": true,
      "warnings": []
    }
  }
}
```

### 2. Get Template Processing Status

Check the processing status of an uploaded template.

**Endpoint:** `GET /templates/{templateId}/status`

**Response:**
```json
{
  "success": true,
  "data": {
    "templateId": "uuid-here",
    "status": "processing|completed|failed",
    "progress": 75,
    "error": null
  }
}
```

### 3. Get Template Placeholders

Retrieve detected placeholders for a template.

**Endpoint:** `GET /templates/{templateId}/placeholders`

**Response:**
```json
{
  "success": true,
  "data": {
    "placeholders": [
      {
        "id": "ph1",
        "text": "{{Name}}",
        "suggestedName": "employeeName",
        "suggestedType": "text",
        "confidence": 0.95,
        "context": "Employee Name: {{Name}}",
        "detectionMethod": "explicit",
        "position": {
          "x": 40,
          "y": 100,
          "width": 200,
          "height": 30
        }
      }
    ],
    "totalDetected": 5,
    "detectionMethod": "mixed"
  }
}
```

### 4. Update Placeholder Mapping

Update placeholder configurations before finalizing the template.

**Endpoint:** `PUT /templates/{templateId}/placeholders`

**Request Body:**
```json
{
  "placeholders": [
    {
      "id": "ph1",
      "suggestedName": "fullName",
      "suggestedType": "text"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "updated": 1,
    "validationResult": {
      "isValid": true,
      "errors": [],
      "warnings": []
    }
  }
}
```

### 5. Finalize Template

Convert the processed document into a final template.

**Endpoint:** `POST /templates/{templateId}/finalize`

**Request Body:**
```json
{
  "templateName": "Final Template Name",
  "category": "Reports",
  "subcategory": "Inspection Reports",
  "description": "Custom description"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "templateId": "uuid-here",
    "template": {
      "title": "Final Template Name",
      "category": "Reports",
      "pages": [...],
      "designSettings": {...}
    }
  }
}
```

### 6. List Templates

Get all templates for the authenticated user.

**Endpoint:** `GET /templates`

**Query Parameters:**
- `category` (optional): Filter by category
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset (default: 0)

**Response:**
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "uuid-here",
        "title": "Template Name",
        "category": "Reports",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 10,
    "limit": 50,
    "offset": 0
  }
}
```

### 7. Delete Template

Delete a template.

**Endpoint:** `DELETE /templates/{templateId}`

**Response:**
```json
{
  "success": true,
  "message": "Template deleted successfully"
}
```

## Error Responses

All endpoints return errors in the following format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "Additional error details"
    }
  }
}
```

### Common Error Codes:

- `INVALID_FILE_TYPE`: Unsupported file format
- `FILE_TOO_LARGE`: File exceeds size limit
- `SECURITY_VALIDATION_FAILED`: File failed security checks
- `PROCESSING_FAILED`: Document processing error
- `TEMPLATE_NOT_FOUND`: Template ID not found
- `UNAUTHORIZED`: Invalid or missing authentication
- `VALIDATION_ERROR`: Request validation failed

## Rate Limits

- **Upload Endpoint**: 10 requests per minute per user
- **Other Endpoints**: 100 requests per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 9
X-RateLimit-Reset: 1640995200
```

## Webhooks

Configure webhooks to receive notifications about template processing:

**Webhook URL Configuration:** `POST /webhooks`

**Webhook Events:**
- `template.processing.started`
- `template.processing.completed`
- `template.processing.failed`

**Webhook Payload:**
```json
{
  "event": "template.processing.completed",
  "templateId": "uuid-here",
  "userId": "user-uuid",
  "timestamp": "2024-01-01T00:00:00Z",
  "data": {
    "processingTime": 5.2,
    "placeholdersDetected": 8,
    "detectionMethod": "mixed"
  }
}
```

## SDK Examples

### JavaScript/Node.js

```javascript
const TemplateUploadAPI = require('@your-org/template-upload-sdk');

const client = new TemplateUploadAPI({
  apiKey: 'your-api-key',
  baseURL: 'https://your-app-domain.com/api'
});

// Upload template
const result = await client.uploadTemplate({
  file: fs.createReadStream('document.docx'),
  templateName: 'My Template',
  category: 'Reports'
});

console.log('Template ID:', result.templateId);

// Check status
const status = await client.getTemplateStatus(result.templateId);
console.log('Status:', status.status);

// Finalize template
const template = await client.finalizeTemplate(result.templateId, {
  templateName: 'Final Name',
  category: 'Reports'
});
```

### Python

```python
from template_upload_sdk import TemplateUploadClient

client = TemplateUploadClient(
    api_key='your-api-key',
    base_url='https://your-app-domain.com/api'
)

# Upload template
with open('document.docx', 'rb') as f:
    result = client.upload_template(
        file=f,
        template_name='My Template',
        category='Reports'
    )

print(f"Template ID: {result.template_id}")

# Check status
status = client.get_template_status(result.template_id)
print(f"Status: {status.status}")

# Finalize template
template = client.finalize_template(
    result.template_id,
    template_name='Final Name',
    category='Reports'
)
```

## Testing

### Test Endpoints

Use the following endpoints for testing:

- `POST /test/upload` - Upload test documents
- `GET /test/templates` - Get test templates
- `POST /test/reset` - Reset test data

### Sample Test Files

Download sample test files:
- [joint-report-sample.docx](./examples/joint-report-sample.docx)
- [ta-form-sample.pdf](./examples/ta-form-sample.pdf)

## Changelog

### v1.0.0 (2024-01-01)
- Initial API release
- Basic upload and processing functionality
- Security validation
- Placeholder detection

### v1.1.0 (2024-01-15)
- Added webhook support
- Improved error handling
- Added batch processing
- Enhanced security features
