import { Link } from "react-router-dom";
import { ArrowLeft, View, Pencil, Printer, Send, RotateCcw, Undo, Redo, Save, MoreHorizontal, Download } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTemplateContext } from "@/context/TemplateContext";
import { useFormContext } from "@/context/FormContext";
import { useEditorUIContext } from "@/context/EditorUIContext";
import { ThemeToggle } from "./ThemeToggle";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { showError, showSuccess } from "@/utils/toast";
import { generatePdfDocument } from "@/utils/pdfGenerator";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

export const EditorHeader = () => {
  const { 
    template, 
    logo,
    resetTemplate, 
    undo, 
    redo, 
    canUndo, 
    canRedo, 
    updateTemplate, 
  } = useTemplateContext();
  const { formState, runFullValidation } = useFormContext();
  const { 
    isPreviewMode, 
    togglePreviewMode, 
    setIsSendDialogOpen, 
    setIsSaveDialogOpen 
  } = useEditorUIContext();

  const handlePrint = async () => {
    if (!runFullValidation()) { showError("Please fix form errors before printing."); return; }
    try { 
      const doc = await generatePdfDocument(template, formState, logo);
      doc.output("dataurlnewwindow"); 
      showSuccess("PDF opened for printing."); 
    } 
    catch (e) { showError("Failed to generate PDF for printing."); console.error(e); }
  };

  const handleExportTemplate = () => {
    try {
      const templateJson = JSON.stringify(template, null, 2);
      const blob = new Blob([templateJson], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${template.title.replace(/ /g, "_")}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      showSuccess("Template exported successfully!");
    } catch (error) {
      console.error("Export failed:", error);
      showError("Failed to export template.");
    }
  };

  return (
    <header className="bg-card p-4 border-b flex items-center justify-between sticky top-0 z-10">
      <div className="flex items-center gap-4">
        <Button asChild variant="outline" size="sm"><Link to="/"><ArrowLeft className="h-4 w-4" /></Link></Button>
        <h1 className="text-lg font-semibold">{template.title}</h1>
      </div>
      <div className="flex items-center gap-2">
        <ThemeToggle />
        <Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={undo} disabled={!canUndo}><Undo className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent><p>Undo (Cmd/Ctrl+Z)</p></TooltipContent></Tooltip>
        <Tooltip><TooltipTrigger asChild><Button variant="outline" size="icon" onClick={redo} disabled={!canRedo}><Redo className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent><p>Redo (Cmd/Ctrl+Y)</p></TooltipContent></Tooltip>
        
        <Tooltip><TooltipTrigger asChild><Button variant="outline" size="sm" onClick={togglePreviewMode}>
          {isPreviewMode ? (<><Pencil className="h-4 w-4 md:mr-2" /><span className="hidden md:inline">Edit</span></>) : (<><View className="h-4 w-4 md:mr-2" /><span className="hidden md:inline">Preview</span></>)}
        </Button></TooltipTrigger><TooltipContent><p>Toggle Preview Mode</p></TooltipContent></Tooltip>
        
        <Tooltip><TooltipTrigger asChild><Button size="sm" onClick={() => setIsSendDialogOpen(true)}><Send className="h-4 w-4 md:mr-2" /><span className="hidden md:inline">Send</span></Button></TooltipTrigger><TooltipContent><p>Send Document</p></TooltipContent></Tooltip>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">More actions</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handlePrint}>
              <Printer className="mr-2 h-4 w-4" />
              <span>Print</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleExportTemplate}>
              <Download className="mr-2 h-4 w-4" />
              <span>Export Template</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            {template.isCustom && (
              <DropdownMenuItem onClick={updateTemplate}>
                <Save className="mr-2 h-4 w-4" />
                <span>Save</span>
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={() => setIsSaveDialogOpen(true)}>
              <Save className="mr-2 h-4 w-4" />
              <span>Save As...</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="text-red-600 focus:bg-red-100 focus:text-red-700 dark:focus:bg-red-900/50 dark:focus:text-red-400">
                  <RotateCcw className="mr-2 h-4 w-4" />
                  <span>Reset</span>
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader><AlertDialogTitle>Reset Template?</AlertDialogTitle><AlertDialogDescription>This will remove all your custom changes and restore the original template. This action cannot be undone.</AlertDialogDescription></AlertDialogHeader>
                <AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction onClick={resetTemplate}>Reset</AlertDialogAction></AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};