import jsPDF from "jspdf";
import "jspdf-autotable";
import { Template, defaultDesignSettings, Logo, FormField, defaultPageSetup } from "@/data/templates";
import { isFieldVisible } from "./visibility";
import { getPageDimensions } from "./pageUtils";
import QRCode from "qrcode";

const processPlaceholders = (text: string, allFields: FormField[], formState: Record<string, any>): string => {
  if (!text) return '';
  return text.replace(/\{\{([^}]+)\}\}/g, (match, fieldId) => {
    const trimmedFieldId = fieldId.trim();
    const value = formState[trimmedFieldId];
    if (value !== undefined && value !== null && value !== '') {
      return String(value);
    }
    const field = allFields.find(f => f.id === trimmedFieldId);
    if (field?.defaultValue) {
      return String(field.defaultValue);
    }
    return `[${field?.label || trimmedFieldId}]`;
  });
};

export const generatePdfDocument = async (
  template: Template,
  formState: Record<string, any>,
  logo: Logo | null
): Promise<jsPDF> => {
  const pageSetup = template.pageSetup || defaultPageSetup;
  const doc = new jsPDF({ orientation: pageSetup.orientation, unit: "px", format: pageSetup.format });
  const { primaryColor, textColor, accentColor, fontFamily, showTitle } = template.designSettings || defaultDesignSettings;
  const pdfFont = fontFamily.toLowerCase().split(' ')[0].replace('new', '');
  
  const allFields = template.pages.flatMap(p => p.fields);
  const pageDimensions = getPageDimensions(pageSetup.format, pageSetup.orientation);

  for (const [pageIndex, page] of template.pages.entries()) {
    if (pageIndex > 0) {
      doc.addPage(pageSetup.format, pageSetup.orientation);
    }

    doc.setFont(pdfFont);

    if (template.watermark?.enabled) {
      const { text, color, opacity, fontSize, rotation } = template.watermark;
      doc.setFontSize(fontSize);
      doc.setTextColor(color);
      doc.text(text, pageDimensions.width / 2, pageDimensions.height / 2, {
        align: "center",
        angle: rotation,
        baseline: "middle",
      });
      doc.setTextColor(textColor);
      doc.setFontSize(12);
    }
    
    if (logo && pageIndex === 0) { // Only show logo on the first page
      try {
        doc.addImage(logo.src, "PNG", logo.x, logo.y, logo.width, logo.height);
      } catch (e) {
        console.error("Failed to add logo to PDF:", e);
      }
    }

    if ((showTitle ?? true) && pageIndex === 0) { // Only show title on the first page
      doc.setFontSize(22);
      doc.setTextColor(primaryColor);
      doc.text(template.title, pageDimensions.width / 2, 60, { align: "center" });
      doc.setTextColor(textColor);
      doc.setFontSize(12);
    }

    const visibleFields = page.fields.filter(field => isFieldVisible(field, formState) && (field.isVisible ?? true));

    for (const field of visibleFields) {
      const value = formState[field.id];
      doc.setDrawColor(accentColor);
      
      if (field.type === 'table') {
        const tableData = value || [];
        const headers = field.columns?.map(c => c.header) || [];
        const body = tableData.map((row: any) => field.columns?.map(c => row[c.id] || '') || []);
        (doc as any).autoTable({ 
          startX: field.x, 
          startY: field.y, 
          head: [headers], 
          body: body, 
          theme: 'grid', 
          styles: { 
            fillColor: [255, 255, 255], 
            textColor: textColor, 
            lineColor: accentColor, 
            lineWidth: 0.5, 
            font: pdfFont 
          }, 
          headStyles: { 
            fillColor: primaryColor, 
            textColor: [255, 255, 255] 
          } 
        });
      } else if (field.type === 'checkbox') {
        const boxSize = 12;
        const boxY = field.y + (field.height - boxSize) / 2;
        doc.rect(field.x, boxY, boxSize, boxSize);
        if (value) {
            doc.setLineWidth(1.5);
            doc.line(field.x + 2, boxY + 6, field.x + 5, boxY + 9);
            doc.line(field.x + 5, boxY + 9, field.x + 10, boxY + 4);
        }
        doc.setFontSize(12);
        doc.setTextColor(textColor);
        doc.text(field.placeholder || field.label, field.x + boxSize + 4, field.y + field.height / 2 + 4);
      } else if (field.type === 'radio') {
        doc.setFontSize(8); doc.setTextColor(textColor); doc.text(field.label, field.x, field.y - 2);
        const optionHeight = 18;
        (field.options || []).forEach((option, index) => {
            const optionY = field.y + (index * optionHeight) + 8;
            doc.circle(field.x + 6, optionY, 5); // Draw circle
            if (value === option) {
                doc.circle(field.x + 6, optionY, 2, 'F'); // Fill if selected
            }
            doc.setFontSize(12);
            doc.setTextColor(textColor);
            doc.text(option, field.x + 15, optionY + 4);
        });
      } else if (field.type === 'box') {
        const hasFill = field.color && field.color !== 'transparent';
        const hasBorder = field.borderWidth && field.borderWidth > 0 && field.borderColor && field.borderColor !== 'transparent';
        
        let style: 'F' | 'S' | 'DF' | undefined = undefined;
        if (hasFill && hasBorder) style = 'DF';
        else if (hasFill) style = 'F';
        else if (hasBorder) style = 'S';

        if (style) {
            doc.setLineWidth(field.borderWidth || 0);
            doc.setDrawColor(field.borderColor || accentColor);
            doc.setFillColor(field.color || primaryColor);
            doc.rect(field.x, field.y, field.width, field.height, style);
        }
      } else if (field.type !== 'static-text') {
        doc.rect(field.x, field.y, field.width, field.height);
        doc.setFontSize(8); doc.setTextColor(textColor); doc.text(field.label, field.x, field.y - 2);
      }
      
      doc.setFontSize(12); doc.setTextColor(textColor); doc.setFont(pdfFont);
      
      if (field.type === "signature" && value) {
        doc.addImage(value, "PNG", field.x, field.y, field.width, field.height);
      } else if (field.type === "image" && value) {
        try {
          const imgFormat = value.substring(value.indexOf('/') + 1, value.indexOf(';'));
          doc.addImage(value, imgFormat.toUpperCase(), field.x, field.y, field.width, field.height);
        } catch (e) {
          console.error(`Failed to add image for field ${field.id}:`, e);
          doc.setDrawColor(255, 0, 0);
          doc.rect(field.x, field.y, field.width, field.height);
          doc.text("Image Error", field.x + 2, field.y + 12);
          doc.setDrawColor(accentColor);
        }
      } else if (field.type === 'qrcode') {
        const qrContent = processPlaceholders(field.defaultValue as string, allFields, formState);
        if (qrContent) {
          try {
            const dataUrl = await QRCode.toDataURL(qrContent);
            doc.addImage(dataUrl, 'PNG', field.x, field.y, field.width, field.height);
          } catch (err) {
            console.error('Failed to generate QR code for PDF', err);
          }
        }
      } else if (field.type === 'static-text') {
        doc.setFontSize(field.fontSize || 12); doc.setTextColor(field.color || textColor);
        let fontStyle = (field.fontWeight === "bold" ? "bold" : "") + (field.fontStyle === "italic" ? "italic" : "");
        doc.setFont(pdfFont, fontStyle || "normal");
        const processedText = processPlaceholders(field.defaultValue as string, allFields, formState);
        doc.text(processedText, field.x + 2, field.y + (field.fontSize || 12), { maxWidth: field.width - 4, align: field.textAlign || "left" });
      } else if (value && field.type !== 'table' && field.type !== 'checkbox' && field.type !== 'radio') {
        doc.text(String(value), field.x + 2, field.y + 12, { maxWidth: field.width - 4 });
      }
    }
  }
  
  return doc;
};