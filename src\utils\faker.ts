// A simple data generation utility
const firstNames = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
const lastNames = ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"];
const streetNames = ["Main St", "Oak Ave", "Pine Ln", "Maple Rd", "Cedar Blvd", "Elm Ct"];
const cities = ["Springfield", "Metropolis", "Gotham", "Star City", "Central City"];
const states = ["CA", "NY", "TX", "FL", "IL"];
const productNouns = ["Widget", "Gadget", "Thingamajig", "Doohickey", "Contraption"];
const productAdjectives = ["Super", "Ultimate", "Advanced", "Portable", "Wireless"];
const lorem = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.";

const randomItem = <T>(arr: T[]): T => arr[Math.floor(Math.random() * arr.length)];

export const faker = {
  name: () => `${randomItem(firstNames)} ${randomItem(lastNames)}`,
  companyName: () => `${randomItem(lastNames)} & Co.`,
  email: () => `${randomItem(firstNames).toLowerCase()}.${randomItem(lastNames).toLowerCase()}@example.com`,
  address: () => `${Math.floor(Math.random() * 1000) + 100} ${randomItem(streetNames)}, ${randomItem(cities)}, ${randomItem(states)} ${String(Math.floor(Math.random() * 90000) + 10000)}`,
  date: () => {
    const date = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
    return date.toISOString().split('T')[0];
  },
  number: (min = 1, max = 100) => Math.floor(Math.random() * (max - min + 1)) + min,
  sentence: () => lorem.split('. ')[0] + '.',
  paragraph: () => lorem,
  productName: () => `${randomItem(productAdjectives)} ${randomItem(productNouns)}`,
  boolean: () => Math.random() > 0.5,
};